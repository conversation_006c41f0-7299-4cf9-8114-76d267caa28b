/**
 * Swagger Configuration
 * Configures API documentation using Swagger/OpenAPI
 */
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'AI Skill Event Management API',
      version: '1.0.0',
      description: 'A comprehensive event management API with authentication, rate limiting, and external integrations',
      contact: {
        name: 'AI Skill Team',
        email: '<EMAIL>'
      },
      license: {
        name: 'ISC',
        url: 'https://opensource.org/licenses/ISC'
      }
    },
    servers: [
      {
        url: process.env.API_BASE_URL || 'http://localhost:3000',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        Event: {
          type: 'object',
          required: ['title', 'startDateTime', 'endDateTime'],
          properties: {
            id: {
              type: 'string',
              description: 'Event ID'
            },
            title: {
              type: 'string',
              maxLength: 200,
              description: 'Event title'
            },
            description: {
              type: 'string',
              maxLength: 2000,
              description: 'Event description'
            },
            startDateTime: {
              type: 'string',
              format: 'date-time',
              description: 'Event start date and time'
            },
            endDateTime: {
              type: 'string',
              format: 'date-time',
              description: 'Event end date and time'
            },
            location: {
              type: 'string',
              maxLength: 500,
              description: 'Event location'
            },
            organizer: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                email: { type: 'string', format: 'email' }
              }
            },
            attendees: {
              type: 'array',
              items: {
                type: 'string',
                format: 'email'
              },
              description: 'List of attendee emails'
            },
            maxAttendees: {
              type: 'integer',
              minimum: 1,
              description: 'Maximum number of attendees'
            },
            status: {
              type: 'string',
              enum: ['active', 'cancelled', 'completed'],
              description: 'Event status'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation timestamp'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last update timestamp'
            }
          }
        },
        User: {
          type: 'object',
          required: ['email', 'firstName', 'lastName'],
          properties: {
            id: {
              type: 'string',
              description: 'User ID'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address'
            },
            firstName: {
              type: 'string',
              maxLength: 50,
              description: 'User first name'
            },
            lastName: {
              type: 'string',
              maxLength: 50,
              description: 'User last name'
            },
            role: {
              type: 'string',
              enum: ['user', 'organizer', 'admin'],
              description: 'User role'
            },
            isActive: {
              type: 'boolean',
              description: 'Whether the user account is active'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Creation timestamp'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last update timestamp'
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: false
            },
            message: {
              type: 'string',
              description: 'Error message'
            },
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  field: { type: 'string' },
                  message: { type: 'string' },
                  value: { type: 'string' }
                }
              }
            }
          }
        },
        Success: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            message: {
              type: 'string',
              description: 'Success message'
            },
            data: {
              type: 'object',
              description: 'Response data'
            }
          }
        },
        PaginatedResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true
            },
            data: {
              type: 'array',
              items: {}
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer' },
                limit: { type: 'integer' },
                total: { type: 'integer' },
                pages: { type: 'integer' }
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ['./src/infrastructure/web/routes/*.js', './src/infrastructure/web/controllers/*.js']
};

const specs = swaggerJsdoc(options);

module.exports = {
  specs,
  swaggerUi,
  options: {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'AI Skill Event Management API Documentation'
  }
};
