/**
 * Jest test setup file
 * Configures global test environment and mocks
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.MONGODB_URI = 'mongodb://localhost:27017/ai-skill-test';
process.env.REDIS_URL = 'redis://localhost:6379/1';
process.env.GOOGLE_MAPS_API_KEY = 'test-google-maps-key';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test utilities
global.testUtils = {
  // Helper to create mock user
  createMockUser: (overrides = {}) => ({
    id: 'user123',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    role: 'user',
    status: 'active',
    createdAt: new Date(),
    ...overrides
  }),

  // Helper to create mock event
  createMockEvent: (overrides = {}) => ({
    id: 'event123',
    title: 'Test Event',
    description: 'Test event description',
    startDateTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    endDateTime: new Date(Date.now() + 25 * 60 * 60 * 1000), // Tomorrow + 1 hour
    location: 'Test Location',
    organizerId: 'organizer123',
    organizer: {
      id: 'organizer123',
      name: 'Test Organizer',
      email: '<EMAIL>'
    },
    attendees: [],
    maxAttendees: 100,
    status: 'published',
    createdAt: new Date(),
    ...overrides
  }),

  // Helper to create mock JWT payload
  createMockJWTPayload: (overrides = {}) => ({
    userId: 'user123',
    email: '<EMAIL>',
    role: 'user',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour from now
    ...overrides
  }),

  // Helper to create mock request object
  createMockRequest: (overrides = {}) => ({
    body: {},
    params: {},
    query: {},
    headers: {},
    user: null,
    ...overrides
  }),

  // Helper to create mock response object
  createMockResponse: () => {
    const res = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.set = jest.fn().mockReturnValue(res);
    return res;
  },

  // Helper to create mock next function
  createMockNext: () => jest.fn(),

  // Helper to wait for async operations
  wait: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to generate random email
  randomEmail: () => `test${Math.random().toString(36).substr(2, 9)}@example.com`,

  // Helper to generate random string
  randomString: (length = 10) => Math.random().toString(36).substr(2, length)
};

// Mock external dependencies that shouldn't be called in tests
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' })
  }))
}));

// Mock Redis client
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
    incr: jest.fn(),
    expire: jest.fn(),
    ttl: jest.fn(),
    disconnect: jest.fn(),
    on: jest.fn(),
    status: 'ready'
  }));
});

// Mock Mongoose connection
jest.mock('mongoose', () => ({
  connect: jest.fn().mockResolvedValue({}),
  disconnect: jest.fn().mockResolvedValue({}),
  connection: {
    readyState: 1,
    on: jest.fn(),
    once: jest.fn()
  },
  Schema: jest.fn(),
  model: jest.fn(),
  Types: {
    ObjectId: jest.fn()
  }
}));

// Global beforeEach and afterEach hooks
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
});

afterEach(() => {
  // Clean up after each test
  jest.restoreAllMocks();
});

// Global error handler for unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process in tests, just log the error
});

// Suppress deprecation warnings in tests
process.env.NODE_NO_WARNINGS = '1';
