# AI Skill Event Management API

A comprehensive event management API built with Node.js, following Clean Architecture and Hexagonal Architecture principles.

## Features

- **Event Management**: Complete CRUD operations for events
- **Authentication**: JWT-based authentication system
- **Rate Limiting**: Redis-based rate limiting (10 requests/minute per IP)
- **Caching**: Redis caching for improved performance
- **Email Notifications**: Automated email notifications for event registration/cancellation
- **Google Maps Integration**: Distance Matrix API integration with caching
- **QR Code Generation**: Generate QR codes for events
- **Reporting**: Statistics and reporting with pagination
- **Multi-Environment Support**: Development, staging, and production configurations

## Architecture

This project follows Clean Architecture and Hexagonal Architecture principles:

```
src/
├── domain/                 # Business logic and entities
│   ├── entities/          # Domain entities
│   ├── repositories/      # Repository interfaces
│   └── services/          # Domain services
├── application/           # Application use cases
│   └── usecases/         # Use case implementations
├── infrastructure/       # External concerns
│   ├── database/         # Database implementations
│   ├── web/              # Web layer (controllers, routes, middleware)
│   ├── external/         # External service integrations
│   └── cache/            # Caching implementations
└── config/               # Configuration files
```

## Prerequisites

- Node.js (v14 or higher)
- MongoDB
- Redis
- Google Maps API key (optional, for distance matrix features)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ai-skill
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment variables:
```bash
cp .env.example .env
```

4. Update the `.env` file with your configuration:
   - Set your MongoDB connection string
   - Set your Redis connection details
   - Set a secure JWT secret
   - Configure email settings
   - Add your Google Maps API key

## Running the Application

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Testing
```bash
npm test
```

## API Documentation

Once the application is running, you can access the Swagger documentation at:
```
http://localhost:3000/api-docs
```

## Git Workflow

This project follows Git Flow branching strategy:

### Branch Types
- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: Feature development branches
- `release/*`: Release preparation branches
- `hotfix/*`: Critical bug fixes

### Branch Naming Conventions
- Feature branches: `feature/description-of-feature`
- Bug fix branches: `bugfix/description-of-bug`
- Release branches: `release/version-number`
- Hotfix branches: `hotfix/description-of-hotfix`

### Commit Message Guidelines
```
<type>(<scope>): <subject>

<body>

<footer>
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Example:
```
feat(auth): add JWT token refresh endpoint

Implement token refresh functionality to allow users to extend
their session without re-authenticating.

Closes #123
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment (development/staging/production) | development |
| `PORT` | Server port | 3000 |
| `DATABASE_URL` | MongoDB connection string | mongodb://localhost:27017/ai-skill |
| `REDIS_URL` | Redis connection string | redis://localhost:6379 |
| `JWT_SECRET` | JWT signing secret | (required in production) |
| `GOOGLE_MAPS_API_KEY` | Google Maps API key | (optional) |

## Rate Limiting

The API implements rate limiting with the following defaults:
- General endpoints: 10 requests per minute per IP
- Authentication endpoints: 5 requests per 15 minutes per IP
- External API endpoints: 3 requests per minute per IP

## Testing

The project includes comprehensive unit tests with >90% coverage for authentication APIs.

Run tests:
```bash
npm test
```

Run tests with coverage:
```bash
npm run test:coverage
```

## Contributing

1. Create a feature branch from `develop`
2. Make your changes
3. Write/update tests
4. Ensure all tests pass
5. Create a pull request to `develop`

## License

This project is licensed under the ISC License.
