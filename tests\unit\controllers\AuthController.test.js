/**
 * Unit tests for AuthController
 */
const AuthController = require('../../../src/infrastructure/web/controllers/AuthController');

describe('AuthController', () => {
  let authController;
  let mockAuthenticateUserUseCase;
  let mockRegisterUserUseCase;
  let mockGetUserUseCase;
  let mockReq;
  let mockRes;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock use cases
    mockAuthenticateUserUseCase = {
      execute: jest.fn()
    };

    mockRegisterUserUseCase = {
      execute: jest.fn()
    };

    mockGetUserUseCase = {
      execute: jest.fn()
    };

    // Mock request and response objects
    mockReq = {
      body: {},
      user: {}
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };

    authController = new AuthController(
      mockAuthenticateUserUseCase,
      mockRegisterUserUseCase,
      mockGetUserUseCase
    );
  });

  describe('login', () => {
    it('should successfully login user with valid credentials', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };
      const authResult = {
        token: 'jwt-token',
        user: {
          id: 'user123',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: 'user'
        }
      };

      mockReq.body = loginData;
      mockAuthenticateUserUseCase.execute.mockResolvedValue(authResult);

      // Act
      await authController.login(mockReq, mockRes);

      // Assert
      expect(mockAuthenticateUserUseCase.execute).toHaveBeenCalledWith(
        loginData.email,
        loginData.password
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: authResult,
        message: 'Login successful'
      });
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should return 401 for invalid credentials', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      mockReq.body = loginData;
      mockAuthenticateUserUseCase.execute.mockRejectedValue(
        new Error('Invalid email or password')
      );

      // Act
      await authController.login(mockReq, mockRes);

      // Assert
      expect(mockAuthenticateUserUseCase.execute).toHaveBeenCalledWith(
        loginData.email,
        loginData.password
      );
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid email or password'
      });
    });

    it('should return 401 for inactive account', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      mockReq.body = loginData;
      mockAuthenticateUserUseCase.execute.mockRejectedValue(
        new Error('Account is deactivated')
      );

      // Act
      await authController.login(mockReq, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Account is deactivated'
      });
    });

    it('should return 401 for suspended account', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      mockReq.body = loginData;
      mockAuthenticateUserUseCase.execute.mockRejectedValue(
        new Error('Account is deactivated')
      );

      // Act
      await authController.login(mockReq, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Account is deactivated'
      });
    });

    it('should return 401 for authentication errors', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      mockReq.body = loginData;
      mockAuthenticateUserUseCase.execute.mockRejectedValue(
        new Error('Invalid credentials')
      );

      // Act
      await authController.login(mockReq, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid credentials'
      });
    });

    it('should handle missing use case gracefully', async () => {
      // Arrange
      const authControllerWithoutUseCase = new AuthController(null, mockRegisterUserUseCase, mockGetUserUseCase);
      mockReq.body = { email: '<EMAIL>', password: 'password123' };

      // Act
      await authControllerWithoutUseCase.login(mockReq, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: "Cannot read properties of null (reading 'execute')"
      });
    });
  });

  describe('register', () => {
    it('should successfully register a new user', async () => {
      // Arrange
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe'
      };
      const registerResult = {
        token: 'jwt-token',
        user: {
          id: 'user123',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          role: 'user'
        }
      };

      mockReq.body = registerData;
      mockRegisterUserUseCase.execute.mockResolvedValue(registerResult);

      // Act
      await authController.register(mockReq, mockRes);

      // Assert
      expect(mockRegisterUserUseCase.execute).toHaveBeenCalledWith(registerData);
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: registerResult,
        message: 'User registered successfully'
      });
    });

    it('should return 400 for existing user', async () => {
      // Arrange
      const registerData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe'
      };

      mockReq.body = registerData;
      mockRegisterUserUseCase.execute.mockRejectedValue(
        new Error('User with this email already exists')
      );

      // Act
      await authController.register(mockReq, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'User with this email already exists'
      });
    });

    it('should return 400 for validation errors', async () => {
      // Arrange
      const registerData = {
        email: 'invalid-email',
        password: '123',
        firstName: '',
        lastName: 'Doe'
      };

      mockReq.body = registerData;
      mockRegisterUserUseCase.execute.mockRejectedValue(
        new Error('Invalid email format')
      );

      // Act
      await authController.register(mockReq, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid email format'
      });
    });

    it('should handle missing use case gracefully', async () => {
      // Arrange
      const authControllerWithoutUseCase = new AuthController(mockAuthenticateUserUseCase, null, mockGetUserUseCase);
      mockReq.body = { email: '<EMAIL>', password: 'password123' };

      // Act
      await authControllerWithoutUseCase.register(mockReq, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(501);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Registration is not implemented yet'
      });
    });
  });

  describe('getProfile', () => {
    it('should successfully get user profile', async () => {
      // Arrange
      const userId = 'user123';
      const userProfile = {
        id: userId,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'user',
        status: 'active'
      };

      mockReq.user = { userId };
      mockGetUserUseCase.execute.mockResolvedValue(userProfile);

      // Act
      await authController.getProfile(mockReq, mockRes);

      // Assert
      expect(mockGetUserUseCase.execute).toHaveBeenCalledWith(userId);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: userProfile
      });
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should return 500 for errors', async () => {
      // Arrange
      const userId = 'nonexistent';
      mockReq.user = { userId };
      mockGetUserUseCase.execute.mockRejectedValue(
        new Error('User not found')
      );

      // Act
      await authController.getProfile(mockReq, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'User not found'
      });
    });

    it('should handle missing use case gracefully', async () => {
      // Arrange
      const authControllerWithoutUseCase = new AuthController(mockAuthenticateUserUseCase, mockRegisterUserUseCase, null);
      mockReq.user = { userId: 'user123' };

      // Act
      await authControllerWithoutUseCase.getProfile(mockReq, mockRes);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(501);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Get user profile is not implemented yet'
      });
    });
  });
});
