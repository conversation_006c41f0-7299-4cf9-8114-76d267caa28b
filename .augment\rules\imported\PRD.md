---
type: "manual"
---

# Project Requirement Document (PRD)

## **A. PART 1: INITIALIZE AND CONFIGURE PROJECT BASE**

### 1. Initialize Project Base
- Create a project with a folder structure following best design practices, prioritizing:
  - **Clean Architecture**
  - **Hexagonal Architecture**
- Goal: ensure scalability, maintainability, and testability.

### 2. Configure Version Control with Git Workflow
- Initialize a local Git repository for the project.
- Set up a working process with Git:
  - **Branching strategy**: recommend Git Flow or Trunk-based Development.
  - Define branch naming conventions and clear commit message guidelines.

### 3. Configure Code Formatter and Swagger
- Install and configure a code formatter (such as <PERSON><PERSON><PERSON>, ESLint, StyleCop…) compatible with the chosen framework.
- Install and configure Swagger to automatically generate API documentation.

### 4. Set Up Multi-Environment Configuration
- Configure environments separately for:
  - Development
  - Staging
  - Production
- Integrate security middleware:
  - **Rate Limiting**
    - Limit: maximum of 10 requests per minute for each IP address.
    - Use Redis to store and track requests for better performance.

### 5. Implement Authentication
- API access authentication
  - Use JWT or OAuth2 depending on the technology stack.
  - Ensure token validation for all protected routes.

### 6. Integrate Public API with Caching
- Create an endpoint to fetch data via Distance Matrix API (Google Maps):
  - https://developers.google.com/maps/documentation/distance-matrix/overview
- Integrate caching (e.g., Redis) to reduce the number of requests to the external API.
- Validate input parameters and cache responses based on those parameters.

---

## **B. PART 2: DESIGN & IMPLEMENT MAIN FEATURES**

### 1. Event Management API
- **CRUD Event API**
  - **Create**: add events, optionally send confirmation emails to attendees after registration.
  - **Read**: search and view event details.
  - **Update**: update event information.
  - **Delete**: delete events.
- Advanced features:
  - Paging
  - Caching
  - Check for schedule conflicts by date/time and end time
  - Send notification emails (via SMTP or third-party service)

### 2. QR Code Generation API
- Endpoint to generate QR codes for events or related data.
- Configure QR code to comply with common QR Code v2 standards.

### 3. Reporting & Statistics API
- Generate reports by day or by specific content
- Design API for paginated reporting results
- Optionally export data (JSON, CSV…)

### 4. Write Unit Tests for API Authentication
- Use AI-generated test cases automatically.
- At least one Authentication API must be unit tested with coverage > 90%.
- Test scenarios:
  - Successful login
  - Failed login
  - Expired token
  - Invalid access rights

---

## **Appendix**

- **Framework**: .NET, NodeJS, or suitable language (to be finalized)
- **Database**: PostgreSQL/MySQL/MongoDB
- **CI/CD**: recommend integrating an automatic build/test/deploy pipeline
- **DevOps**: Docker, Kubernetes (if needed)
- **Monitoring**: integrate Prometheus, Grafana (if applicable)

---

**End of Document**
