/**
 * Application Configuration
 * Manages environment-specific configurations
 */
require('dotenv').config();

const config = {
  // Environment
  env: process.env.NODE_ENV || 'development',
  port: process.env.PORT || 3000,

  // Database
  database: {
    url: process.env.DATABASE_URL || 'mongodb://localhost:27017/ai-skill',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    }
  },

  // Redis
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: process.env.REDIS_DB || 0
  },

  // JWT
  jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',

  // Email
  email: {
    service: process.env.EMAIL_SERVICE || 'smtp',
    host: process.env.EMAIL_HOST || 'smtp.ethereal.email',
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER || '<EMAIL>',
    password: process.env.EMAIL_PASSWORD || 'password',
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },

  // Google Maps
  googleMaps: {
    apiKey: process.env.GOOGLE_MAPS_API_KEY || 'your-google-maps-api-key'
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60000, // 1 minute
    max: parseInt(process.env.RATE_LIMIT_MAX) || 10, // 10 requests per minute
    authWindowMs: parseInt(process.env.AUTH_RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
    authMax: parseInt(process.env.AUTH_RATE_LIMIT_MAX) || 5 // 5 attempts per 15 minutes
  },

  // CORS
  cors: {
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true
  },

  // Swagger
  swagger: {
    title: 'AI Skill Event Management API',
    description: 'A comprehensive event management API with authentication, rate limiting, and external integrations',
    version: '1.0.0',
    servers: [
      {
        url: process.env.API_BASE_URL || 'http://localhost:3000',
        description: 'Development server'
      }
    ]
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined'
  },

  // Security
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    sessionSecret: process.env.SESSION_SECRET || 'your-session-secret-change-this-in-production'
  }
};

// Environment-specific overrides
if (config.env === 'production') {
  // Production-specific configurations
  config.logging.level = 'warn';
  config.database.options.ssl = true;
  config.database.options.maxPoolSize = 50;
  config.rateLimit.max = 5; // Stricter rate limiting in production
  config.rateLimit.authMax = 3;
} else if (config.env === 'staging') {
  // Staging-specific configurations
  config.database.url = process.env.DATABASE_URL || 'mongodb://localhost:27017/ai-skill-staging';
  config.logging.level = 'info';
  config.rateLimit.max = 20; // More lenient for staging
  config.rateLimit.authMax = 10;
} else if (config.env === 'test') {
  // Test-specific configurations
  config.database.url = process.env.TEST_DATABASE_URL || 'mongodb://localhost:27017/ai-skill-test';
  config.redis.db = 15; // Use different Redis DB for tests
  config.logging.level = 'error';
  config.jwtExpiresIn = '1h'; // Shorter expiry for tests
} else if (config.env === 'development') {
  // Development-specific configurations
  config.logging.level = 'debug';
  config.logging.format = 'dev';
  config.cors.origin = ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8080'];
}

// Validation
function validateConfig() {
  const requiredEnvVars = [];

  if (config.env === 'production') {
    requiredEnvVars.push(
      'DATABASE_URL',
      'JWT_SECRET',
      'REDIS_URL'
    );
  }

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  if (config.jwtSecret === 'your-super-secret-jwt-key-change-this-in-production' && config.env === 'production') {
    throw new Error('JWT_SECRET must be set in production');
  }
}

// Validate configuration on load
validateConfig();

module.exports = config;
