/**
 * Unit tests for AuthenticateUserUseCase
 */
const AuthenticateUserUseCase = require('../../../src/application/usecases/AuthenticateUserUseCase');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

// Mock dependencies
jest.mock('bcrypt');
jest.mock('jsonwebtoken');

describe('AuthenticateUserUseCase', () => {
  let authenticateUserUseCase;
  let mockUserRepository;
  let mockConfig;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock user repository
    mockUserRepository = {
      findByEmail: jest.fn()
    };

    // Mock config
    mockConfig = {
      jwtSecret: 'test-secret',
      jwtExpiresIn: '1h'
    };

    authenticateUserUseCase = new AuthenticateUserUseCase(mockUserRepository, mockConfig);
  });

  describe('execute', () => {
    const validEmail = '<EMAIL>';
    const validPassword = 'password123';
    const hashedPassword = '$2b$10$hashedpassword';

    const mockUser = {
      id: 'user123',
      email: validEmail,
      password: hashedPassword,
      firstName: 'John',
      lastName: 'Doe',
      role: 'user',
      status: 'active',
      isActive: true
    };

    it('should successfully authenticate user with valid credentials', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      bcrypt.compare.mockResolvedValue(true);
      jwt.sign.mockReturnValue('mock-jwt-token');

      // Act
      const result = await authenticateUserUseCase.execute(validEmail, validPassword);

      // Assert
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
      expect(bcrypt.compare).toHaveBeenCalledWith(validPassword, hashedPassword);
      expect(jwt.sign).toHaveBeenCalledWith(
        {
          userId: mockUser.id,
          email: mockUser.email,
          role: mockUser.role
        },
        mockConfig.jwtSecret,
        { expiresIn: mockConfig.jwtExpiresIn }
      );

      expect(result).toEqual({
        token: 'mock-jwt-token',
        user: {
          id: mockUser.id,
          email: mockUser.email,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          role: mockUser.role,
          status: mockUser.status,
          isActive: mockUser.isActive
        },
        expiresIn: mockConfig.jwtExpiresIn
      });
    });

    it('should throw error when user is not found', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null);

      // Act & Assert
      await expect(authenticateUserUseCase.execute(validEmail, validPassword))
        .rejects.toThrow('Invalid credentials');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
      expect(bcrypt.compare).not.toHaveBeenCalled();
      expect(jwt.sign).not.toHaveBeenCalled();
    });

    it('should throw error when password is incorrect', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      bcrypt.compare.mockResolvedValue(false);

      // Act & Assert
      await expect(authenticateUserUseCase.execute(validEmail, 'wrongpassword'))
        .rejects.toThrow('Invalid credentials');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
      expect(bcrypt.compare).toHaveBeenCalledWith('wrongpassword', hashedPassword);
      expect(jwt.sign).not.toHaveBeenCalled();
    });

    it('should throw error when user account is inactive', async () => {
      // Arrange
      const inactiveUser = { ...mockUser, isActive: false };
      mockUserRepository.findByEmail.mockResolvedValue(inactiveUser);

      // Act & Assert
      await expect(authenticateUserUseCase.execute(validEmail, validPassword))
        .rejects.toThrow('Account is deactivated');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
      expect(bcrypt.compare).not.toHaveBeenCalled();
      expect(jwt.sign).not.toHaveBeenCalled();
    });

    it('should throw error when user account is suspended', async () => {
      // Arrange
      const suspendedUser = { ...mockUser, isActive: false };
      mockUserRepository.findByEmail.mockResolvedValue(suspendedUser);

      // Act & Assert
      await expect(authenticateUserUseCase.execute(validEmail, validPassword))
        .rejects.toThrow('Account is deactivated');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
      expect(bcrypt.compare).not.toHaveBeenCalled();
      expect(jwt.sign).not.toHaveBeenCalled();
    });

    it('should throw error with missing email', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null);

      // Act & Assert
      await expect(authenticateUserUseCase.execute('', validPassword))
        .rejects.toThrow('Invalid credentials');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith('');
    });

    it('should throw error with missing password', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      bcrypt.compare.mockResolvedValue(false);

      // Act & Assert
      await expect(authenticateUserUseCase.execute(validEmail, ''))
        .rejects.toThrow('Invalid credentials');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
    });

    it('should handle repository errors gracefully', async () => {
      // Arrange
      const repositoryError = new Error('Database connection failed');
      mockUserRepository.findByEmail.mockRejectedValue(repositoryError);

      // Act & Assert
      await expect(authenticateUserUseCase.execute(validEmail, validPassword))
        .rejects.toThrow('Database connection failed');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
    });

    it('should handle bcrypt errors gracefully', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      const bcryptError = new Error('Bcrypt comparison failed');
      bcrypt.compare.mockRejectedValue(bcryptError);

      // Act & Assert
      await expect(authenticateUserUseCase.execute(validEmail, validPassword))
        .rejects.toThrow('Bcrypt comparison failed');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
      expect(bcrypt.compare).toHaveBeenCalledWith(validPassword, hashedPassword);
    });

    it('should handle JWT signing errors gracefully', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      bcrypt.compare.mockResolvedValue(true);
      const jwtError = new Error('JWT signing failed');
      jwt.sign.mockImplementation(() => {
        throw jwtError;
      });

      // Act & Assert
      await expect(authenticateUserUseCase.execute(validEmail, validPassword))
        .rejects.toThrow('JWT signing failed');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validEmail);
      expect(bcrypt.compare).toHaveBeenCalledWith(validPassword, hashedPassword);
      expect(jwt.sign).toHaveBeenCalled();
    });

    it('should not include password in returned user object', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      bcrypt.compare.mockResolvedValue(true);
      jwt.sign.mockReturnValue('mock-jwt-token');

      // Act
      const result = await authenticateUserUseCase.execute(validEmail, validPassword);

      // Assert
      expect(result.user).not.toHaveProperty('password');
      expect(result.user).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
        role: mockUser.role,
        status: mockUser.status,
        isActive: mockUser.isActive
      });
    });
  });
});
