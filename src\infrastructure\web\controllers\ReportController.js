/**
 * Report Controller
 * Handles HTTP requests for reporting and statistics
 */
const { Parser } = require('json2csv');

class ReportController {
  constructor(eventRepository, userRepository) {
    this.eventRepository = eventRepository;
    this.userRepository = userRepository;
  }

  async getEventStatistics(req, res) {
    try {
      const { startDate, endDate, groupBy = 'day' } = req.query;

      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          message: 'Start date and end date are required'
        });
      }

      const start = new Date(startDate);
      const end = new Date(endDate);

      if (start >= end) {
        return res.status(400).json({
          success: false,
          message: 'Start date must be before end date'
        });
      }

      // Get event statistics
      const stats = await this.eventRepository.getEventStatistics(start, end);

      // Get total counts
      const totalEvents = await this.eventRepository.findByDateRange(start, end);
      const activeEvents = totalEvents.filter(event => event.status === 'active');
      const cancelledEvents = totalEvents.filter(event => event.status === 'cancelled');
      const completedEvents = totalEvents.filter(event => event.status === 'completed');

      // Calculate attendee statistics
      const totalAttendees = totalEvents.reduce((sum, event) => sum + event.attendees.length, 0);
      const avgAttendeesPerEvent = totalEvents.length > 0 ? totalAttendees / totalEvents.length : 0;

      // Group events by specified period
      const groupedData = this.groupEventsByPeriod(totalEvents, groupBy);

      res.json({
        success: true,
        data: {
          summary: {
            totalEvents: totalEvents.length,
            activeEvents: activeEvents.length,
            cancelledEvents: cancelledEvents.length,
            completedEvents: completedEvents.length,
            totalAttendees,
            avgAttendeesPerEvent: Math.round(avgAttendeesPerEvent * 100) / 100
          },
          groupedData,
          period: {
            startDate: start.toISOString(),
            endDate: end.toISOString(),
            groupBy
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getEventReport(req, res) {
    try {
      const { 
        startDate, 
        endDate, 
        status, 
        organizerId,
        format = 'json',
        page = 1,
        limit = 50
      } = req.query;

      // Build filters
      const filters = {};
      if (startDate && endDate) {
        filters.startDate = startDate;
        filters.endDate = endDate;
      }
      if (status) filters.status = status;
      if (organizerId) filters.organizerId = organizerId;

      // Get events with pagination
      const pagination = {
        page: parseInt(page),
        limit: parseInt(limit)
      };

      const result = await this.eventRepository.findAll(filters, pagination);

      if (format === 'csv') {
        return this.exportToCSV(res, result.events, 'events-report');
      }

      res.json({
        success: true,
        data: result.events,
        pagination: result.pagination,
        filters
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getAttendeeReport(req, res) {
    try {
      const { 
        startDate, 
        endDate, 
        eventId,
        format = 'json'
      } = req.query;

      let events = [];

      if (eventId) {
        const event = await this.eventRepository.findById(eventId);
        if (!event) {
          return res.status(404).json({
            success: false,
            message: 'Event not found'
          });
        }
        events = [event];
      } else if (startDate && endDate) {
        events = await this.eventRepository.findByDateRange(new Date(startDate), new Date(endDate));
      } else {
        return res.status(400).json({
          success: false,
          message: 'Either eventId or date range (startDate and endDate) is required'
        });
      }

      // Process attendee data
      const attendeeData = [];
      const attendeeStats = new Map();

      events.forEach(event => {
        event.attendees.forEach(attendeeEmail => {
          attendeeData.push({
            eventId: event.id,
            eventTitle: event.title,
            eventDate: event.startDateTime,
            attendeeEmail,
            registrationDate: event.createdAt // Simplified - in real app, track actual registration date
          });

          // Update attendee statistics
          if (attendeeStats.has(attendeeEmail)) {
            attendeeStats.get(attendeeEmail).eventCount++;
          } else {
            attendeeStats.set(attendeeEmail, {
              email: attendeeEmail,
              eventCount: 1,
              lastEventDate: event.startDateTime
            });
          }
        });
      });

      const summary = {
        totalAttendees: attendeeStats.size,
        totalRegistrations: attendeeData.length,
        avgEventsPerAttendee: attendeeStats.size > 0 ? attendeeData.length / attendeeStats.size : 0,
        topAttendees: Array.from(attendeeStats.values())
          .sort((a, b) => b.eventCount - a.eventCount)
          .slice(0, 10)
      };

      if (format === 'csv') {
        return this.exportToCSV(res, attendeeData, 'attendee-report');
      }

      res.json({
        success: true,
        data: {
          summary,
          attendees: attendeeData,
          statistics: Array.from(attendeeStats.values())
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getOrganizerReport(req, res) {
    try {
      const { 
        startDate, 
        endDate,
        format = 'json'
      } = req.query;

      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          message: 'Start date and end date are required'
        });
      }

      const events = await this.eventRepository.findByDateRange(new Date(startDate), new Date(endDate));

      // Group events by organizer
      const organizerStats = new Map();

      events.forEach(event => {
        const organizerId = event.organizer.id;
        
        if (organizerStats.has(organizerId)) {
          const stats = organizerStats.get(organizerId);
          stats.eventCount++;
          stats.totalAttendees += event.attendees.length;
          stats.events.push({
            id: event.id,
            title: event.title,
            startDateTime: event.startDateTime,
            attendeeCount: event.attendees.length,
            status: event.status
          });
        } else {
          organizerStats.set(organizerId, {
            organizerId,
            organizerName: event.organizer.name,
            organizerEmail: event.organizer.email,
            eventCount: 1,
            totalAttendees: event.attendees.length,
            avgAttendeesPerEvent: 0,
            events: [{
              id: event.id,
              title: event.title,
              startDateTime: event.startDateTime,
              attendeeCount: event.attendees.length,
              status: event.status
            }]
          });
        }
      });

      // Calculate averages
      organizerStats.forEach(stats => {
        stats.avgAttendeesPerEvent = stats.eventCount > 0 ? 
          Math.round((stats.totalAttendees / stats.eventCount) * 100) / 100 : 0;
      });

      const organizerData = Array.from(organizerStats.values())
        .sort((a, b) => b.eventCount - a.eventCount);

      if (format === 'csv') {
        const csvData = organizerData.map(org => ({
          organizerName: org.organizerName,
          organizerEmail: org.organizerEmail,
          eventCount: org.eventCount,
          totalAttendees: org.totalAttendees,
          avgAttendeesPerEvent: org.avgAttendeesPerEvent
        }));
        return this.exportToCSV(res, csvData, 'organizer-report');
      }

      res.json({
        success: true,
        data: {
          summary: {
            totalOrganizers: organizerData.length,
            totalEvents: events.length,
            totalAttendees: organizerData.reduce((sum, org) => sum + org.totalAttendees, 0)
          },
          organizers: organizerData
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  groupEventsByPeriod(events, groupBy) {
    const grouped = new Map();

    events.forEach(event => {
      let key;
      const date = new Date(event.startDateTime);

      switch (groupBy) {
        case 'day':
          key = date.toISOString().split('T')[0];
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
        case 'year':
          key = date.getFullYear().toString();
          break;
        default:
          key = date.toISOString().split('T')[0];
      }

      if (grouped.has(key)) {
        const group = grouped.get(key);
        group.eventCount++;
        group.attendeeCount += event.attendees.length;
        group.events.push({
          id: event.id,
          title: event.title,
          attendeeCount: event.attendees.length
        });
      } else {
        grouped.set(key, {
          period: key,
          eventCount: 1,
          attendeeCount: event.attendees.length,
          events: [{
            id: event.id,
            title: event.title,
            attendeeCount: event.attendees.length
          }]
        });
      }
    });

    return Array.from(grouped.values()).sort((a, b) => a.period.localeCompare(b.period));
  }

  exportToCSV(res, data, filename) {
    try {
      if (!data || data.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No data available for export'
        });
      }

      const parser = new Parser();
      const csv = parser.parse(data);

      res.set({
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}-${new Date().toISOString().split('T')[0]}.csv"`
      });

      res.send(csv);
    } catch (error) {
      res.status(500).json({
        success: false,
        message: `CSV export failed: ${error.message}`
      });
    }
  }
}

module.exports = ReportController;
