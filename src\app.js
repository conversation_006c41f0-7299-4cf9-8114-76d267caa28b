/**
 * Main Application Entry Point
 * Sets up Express server with all middleware, routes, and dependencies
 */
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const mongoose = require('mongoose');

// Configuration
const config = require('./config/config');

// Infrastructure
const RedisService = require('./infrastructure/cache/RedisService');
const EmailService = require('./infrastructure/external/EmailService');
const GoogleMapsService = require('./infrastructure/external/GoogleMapsService');

// Repositories
const MongoEventRepository = require('./infrastructure/database/repositories/MongoEventRepository');
const MongoUserRepository = require('./infrastructure/database/repositories/MongoUserRepository');

// Domain Services
const EventService = require('./domain/services/EventService');

// Use Cases
const CreateEventUseCase = require('./application/usecases/CreateEventUseCase');
const GetEventsUseCase = require('./application/usecases/GetEventsUseCase');
const AuthenticateUserUseCase = require('./application/usecases/AuthenticateUserUseCase');

// Web Layer
const EventController = require('./infrastructure/web/controllers/EventController');
const AuthController = require('./infrastructure/web/controllers/AuthController');
const AuthMiddleware = require('./infrastructure/web/middleware/authMiddleware');
const RateLimitMiddleware = require('./infrastructure/web/middleware/rateLimitMiddleware');

// Routes
const createEventRoutes = require('./infrastructure/web/routes/eventRoutes');
const createAuthRoutes = require('./infrastructure/web/routes/authRoutes');

// Swagger
const { specs, swaggerUi, options: swaggerOptions } = require('./infrastructure/web/swagger/swaggerConfig');

class Application {
  constructor() {
    this.app = express();
    this.config = config;
    this.redisService = null;
    this.emailService = null;
    this.googleMapsService = null;
  }

  async initialize() {
    try {
      // Connect to databases
      await this.connectToMongoDB();
      await this.connectToRedis();

      // Initialize services
      this.initializeServices();

      // Setup middleware
      this.setupMiddleware();

      // Setup routes
      this.setupRoutes();

      // Setup error handling
      this.setupErrorHandling();

      console.log('Application initialized successfully');
    } catch (error) {
      console.error('Failed to initialize application:', error);
      process.exit(1);
    }
  }

  async connectToMongoDB() {
    try {
      await mongoose.connect(this.config.database.url, this.config.database.options);
      console.log('Connected to MongoDB');
    } catch (error) {
      console.error('MongoDB connection error:', error);
      throw error;
    }
  }

  async connectToRedis() {
    try {
      this.redisService = new RedisService(this.config);
      await this.redisService.connect();
      console.log('Connected to Redis');
    } catch (error) {
      console.error('Redis connection error:', error);
      throw error;
    }
  }

  initializeServices() {
    // External services
    this.emailService = new EmailService(this.config);
    this.googleMapsService = new GoogleMapsService(this.config.googleMaps.apiKey, this.redisService);

    // Repositories
    this.eventRepository = new MongoEventRepository();
    this.userRepository = new MongoUserRepository();

    // Domain services
    this.eventService = new EventService(this.eventRepository, this.emailService, this.redisService);

    // Use cases
    this.createEventUseCase = new CreateEventUseCase(this.eventService, { getUserById: (id) => this.userRepository.findById(id) });
    this.getEventsUseCase = new GetEventsUseCase(this.eventRepository, this.redisService);
    this.authenticateUserUseCase = new AuthenticateUserUseCase(this.userRepository, this.config);

    // Controllers
    this.eventController = new EventController(
      this.createEventUseCase,
      this.getEventsUseCase,
      null, // updateEventUseCase - to be implemented
      null  // deleteEventUseCase - to be implemented
    );
    this.authController = new AuthController(this.authenticateUserUseCase, null); // registerUserUseCase - to be implemented

    // Middleware
    this.authMiddleware = new AuthMiddleware(this.config, this.userRepository);
    this.rateLimitMiddleware = new RateLimitMiddleware(this.redisService.getClient());
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());
    this.app.use(cors(this.config.cors));

    // Logging
    if (this.config.env !== 'test') {
      this.app.use(morgan(this.config.logging.format));
    }

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Trust proxy for rate limiting
    this.app.set('trust proxy', 1);
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: this.config.env
      });
    });

    // API Documentation
    this.app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerOptions));

    // API Routes
    this.app.use('/api/auth', createAuthRoutes(
      this.authController,
      this.authMiddleware,
      this.rateLimitMiddleware
    ));

    this.app.use('/api/events', createEventRoutes(
      this.eventController,
      this.authMiddleware,
      this.rateLimitMiddleware
    ));

    // Root route
    this.app.get('/', (req, res) => {
      res.json({
        message: 'AI Skill Event Management API',
        version: '1.0.0',
        documentation: '/api-docs',
        health: '/health'
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        message: 'Route not found'
      });
    });
  }

  setupErrorHandling() {
    // Global error handler
    this.app.use((error, req, res, next) => {
      console.error('Unhandled error:', error);

      // Mongoose validation error
      if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map(err => ({
          field: err.path,
          message: err.message,
          value: err.value
        }));

        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors
        });
      }

      // Mongoose duplicate key error
      if (error.code === 11000) {
        const field = Object.keys(error.keyValue)[0];
        return res.status(400).json({
          success: false,
          message: `${field} already exists`
        });
      }

      // JWT errors
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: 'Invalid token'
        });
      }

      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Token expired'
        });
      }

      // Default error response
      res.status(error.status || 500).json({
        success: false,
        message: error.message || 'Internal server error'
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      this.gracefulShutdown();
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown();
    });

    // Handle SIGTERM
    process.on('SIGTERM', () => {
      console.log('SIGTERM received');
      this.gracefulShutdown();
    });

    // Handle SIGINT
    process.on('SIGINT', () => {
      console.log('SIGINT received');
      this.gracefulShutdown();
    });
  }

  async gracefulShutdown() {
    console.log('Starting graceful shutdown...');

    try {
      // Close Redis connection
      if (this.redisService) {
        await this.redisService.disconnect();
      }

      // Close MongoDB connection
      await mongoose.connection.close();

      console.log('Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      console.error('Error during graceful shutdown:', error);
      process.exit(1);
    }
  }

  async start() {
    await this.initialize();

    const port = this.config.port;
    this.server = this.app.listen(port, () => {
      console.log(`Server running on port ${port}`);
      console.log(`Environment: ${this.config.env}`);
      console.log(`API Documentation: http://localhost:${port}/api-docs`);
    });

    return this.server;
  }

  async stop() {
    if (this.server) {
      this.server.close();
    }
    await this.gracefulShutdown();
  }
}

// Start the application if this file is run directly
if (require.main === module) {
  const app = new Application();
  app.start().catch(error => {
    console.error('Failed to start application:', error);
    process.exit(1);
  });
}

module.exports = Application;
