# Environment
NODE_ENV=development
PORT=3000

# Database
DATABASE_URL=mongodb://localhost:27017/ai-skill

# Redis
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Email Configuration
EMAIL_SERVICE=smtp
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=password
EMAIL_FROM=<EMAIL>

# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=10
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX=5

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# API
API_BASE_URL=http://localhost:3000

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this-in-production
