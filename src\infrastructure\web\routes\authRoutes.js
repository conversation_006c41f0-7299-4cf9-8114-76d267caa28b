/**
 * Authentication Routes
 * Defines HTTP routes for authentication operations
 */
const express = require('express');
const { body } = require('express-validator');
const validationMiddleware = require('../middleware/validationMiddleware');

function createAuthRoutes(authController, authMiddleware, rateLimitMiddleware) {
  const router = express.Router();

  // Validation rules
  const loginValidation = [
    body('email')
      .isEmail()
      .withMessage('Valid email is required')
      .normalizeEmail(),
    body('password')
      .notEmpty()
      .withMessage('Password is required')
  ];

  const registerValidation = [
    body('email')
      .isEmail()
      .withMessage('Valid email is required')
      .normalizeEmail(),
    body('password')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    body('firstName')
      .notEmpty()
      .withMessage('First name is required')
      .isLength({ max: 50 })
      .withMessage('First name must be less than 50 characters')
      .trim(),
    body('lastName')
      .notEmpty()
      .withMessage('Last name is required')
      .isLength({ max: 50 })
      .withMessage('Last name must be less than 50 characters')
      .trim(),
    body('role')
      .optional()
      .isIn(['user', 'organizer', 'admin'])
      .withMessage('Invalid role')
  ];

  const refreshTokenValidation = [
    body('token')
      .notEmpty()
      .withMessage('Token is required')
  ];

  // Routes

  /**
   * @swagger
   * /api/auth/login:
   *   post:
   *     summary: User login
   *     tags: [Authentication]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - email
   *               - password
   *             properties:
   *               email:
   *                 type: string
   *                 format: email
   *                 description: User email address
   *               password:
   *                 type: string
   *                 description: User password
   *     responses:
   *       200:
   *         description: Login successful
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *       401:
   *         description: Invalid credentials
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       429:
   *         description: Too many requests
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.post('/login',
    rateLimitMiddleware.auth(),
    loginValidation,
    validationMiddleware,
    (req, res) => authController.login(req, res)
  );

  router.post('/register',
    rateLimitMiddleware.auth(),
    registerValidation,
    validationMiddleware,
    (req, res) => authController.register(req, res)
  );

  router.post('/refresh',
    rateLimitMiddleware.auth(),
    refreshTokenValidation,
    validationMiddleware,
    (req, res) => authController.refreshToken(req, res)
  );

  router.post('/logout',
    rateLimitMiddleware.general(),
    authMiddleware.authenticate(),
    (req, res) => authController.logout(req, res)
  );

  router.get('/profile',
    rateLimitMiddleware.read(),
    authMiddleware.authenticate(),
    (req, res) => authController.getProfile(req, res)
  );

  router.get('/validate',
    rateLimitMiddleware.read(),
    (req, res) => authController.validateToken(req, res)
  );

  return router;
}

module.exports = createAuthRoutes;
