/**
 * Unit tests for AuthMiddleware
 */
const AuthMiddleware = require('../../../src/infrastructure/web/middleware/authMiddleware');
const jwt = require('jsonwebtoken');

// Mock dependencies
jest.mock('jsonwebtoken');

describe('AuthMiddleware', () => {
  let authMiddleware;
  let mockUserRepository;
  let mockConfig;
  let mockReq;
  let mockRes;
  let mockNext;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock user repository
    mockUserRepository = {
      findById: jest.fn()
    };

    // Mock config
    mockConfig = {
      jwtSecret: 'test-secret'
    };

    // Mock request, response, and next
    mockReq = {
      headers: {},
      user: null
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    authMiddleware = new AuthMiddleware(mockConfig, mockUserRepository);
  });

  describe('authenticate', () => {
    const validToken = 'valid-jwt-token';
    const decodedToken = {
      userId: 'user123',
      email: '<EMAIL>',
      role: 'user'
    };
    const mockUser = {
      id: 'user123',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'user',
      status: 'active',
      isActive: true
    };

    it('should authenticate user with valid token', async () => {
      // Arrange
      mockReq.headers.authorization = `Bearer ${validToken}`;
      jwt.verify.mockReturnValue(decodedToken);
      mockUserRepository.findById.mockResolvedValue(mockUser);

      const authenticateMiddleware = authMiddleware.authenticate();

      // Act
      await authenticateMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith(validToken, mockConfig.jwtSecret);
      expect(mockUserRepository.findById).toHaveBeenCalledWith(decodedToken.userId);
      expect(mockReq.user).toEqual({
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
        status: mockUser.status
      });
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should return 401 when no authorization header is provided', async () => {
      // Arrange
      const authenticateMiddleware = authMiddleware.authenticate();

      // Act
      await authenticateMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Access token is required'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when authorization header format is invalid', async () => {
      // Arrange
      mockReq.headers.authorization = 'InvalidFormat token';
      const authenticateMiddleware = authMiddleware.authenticate();

      // Act
      await authenticateMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Access token is required'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when token is invalid', async () => {
      // Arrange
      mockReq.headers.authorization = `Bearer invalid-token`;
      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const authenticateMiddleware = authMiddleware.authenticate();

      // Act
      await authenticateMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith('invalid-token', mockConfig.jwtSecret);
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Authentication error'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when token is expired', async () => {
      // Arrange
      mockReq.headers.authorization = `Bearer expired-token`;
      const expiredError = new Error('Token expired');
      expiredError.name = 'TokenExpiredError';
      jwt.verify.mockImplementation(() => {
        throw expiredError;
      });

      const authenticateMiddleware = authMiddleware.authenticate();

      // Act
      await authenticateMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Token expired'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when user is not found', async () => {
      // Arrange
      mockReq.headers.authorization = `Bearer ${validToken}`;
      jwt.verify.mockReturnValue(decodedToken);
      mockUserRepository.findById.mockResolvedValue(null);

      const authenticateMiddleware = authMiddleware.authenticate();

      // Act
      await authenticateMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockUserRepository.findById).toHaveBeenCalledWith(decodedToken.userId);
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid or expired token'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when user account is inactive', async () => {
      // Arrange
      const inactiveUser = { ...mockUser, isActive: false };
      mockReq.headers.authorization = `Bearer ${validToken}`;
      jwt.verify.mockReturnValue(decodedToken);
      mockUserRepository.findById.mockResolvedValue(inactiveUser);

      const authenticateMiddleware = authMiddleware.authenticate();

      // Act
      await authenticateMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid or expired token'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when user account is suspended', async () => {
      // Arrange
      const suspendedUser = { ...mockUser, isActive: false };
      mockReq.headers.authorization = `Bearer ${validToken}`;
      jwt.verify.mockReturnValue(decodedToken);
      mockUserRepository.findById.mockResolvedValue(suspendedUser);

      const authenticateMiddleware = authMiddleware.authenticate();

      // Act
      await authenticateMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Invalid or expired token'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('authorize', () => {
    beforeEach(() => {
      mockReq.user = {
        userId: 'user123',
        email: '<EMAIL>',
        role: 'user',
        status: 'active'
      };
    });

    it('should authorize user with correct role', async () => {
      // Arrange
      const authorizeMiddleware = authMiddleware.authorize(['user', 'admin']);

      // Act
      await authorizeMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should return 403 when user role is not authorized', async () => {
      // Arrange
      const authorizeMiddleware = authMiddleware.authorize(['admin']);

      // Act
      await authorizeMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Insufficient permissions'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 401 when user is not authenticated', async () => {
      // Arrange
      mockReq.user = null;
      const authorizeMiddleware = authMiddleware.authorize(['user']);

      // Act
      await authorizeMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Authentication required'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 403 when user role is not in required roles', async () => {
      // Arrange
      mockReq.user.role = 'user';
      const authorizeMiddleware = authMiddleware.authorize(['organizer']);

      // Act
      await authorizeMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        message: 'Insufficient permissions'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('optional', () => {
    it('should authenticate user when valid token is provided', async () => {
      // Arrange
      const validToken = 'valid-jwt-token';
      const decodedToken = { userId: 'user123', email: '<EMAIL>', role: 'user' };
      const mockUser = { id: 'user123', email: '<EMAIL>', role: 'user', status: 'active', isActive: true };

      mockReq.headers.authorization = `Bearer ${validToken}`;
      jwt.verify.mockReturnValue(decodedToken);
      mockUserRepository.findById.mockResolvedValue(mockUser);

      const optionalMiddleware = authMiddleware.optional();

      // Act
      await optionalMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockReq.user).toEqual({
        userId: mockUser.id,
        email: mockUser.email,
        role: mockUser.role
      });
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should continue without authentication when no token is provided', async () => {
      // Arrange
      const optionalMiddleware = authMiddleware.optional();

      // Act
      await optionalMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockReq.user).toBeNull();
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should continue without authentication when token is invalid', async () => {
      // Arrange
      mockReq.headers.authorization = 'Bearer invalid-token';
      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const optionalMiddleware = authMiddleware.optional();

      // Act
      await optionalMiddleware(mockReq, mockRes, mockNext);

      // Assert
      expect(mockReq.user).toBeNull();
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });
  });
});
