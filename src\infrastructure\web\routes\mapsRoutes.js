/**
 * Maps Routes
 * Defines HTTP routes for Google Maps Distance Matrix API operations
 */
const express = require('express');
const { body, query } = require('express-validator');
const validationMiddleware = require('../middleware/validationMiddleware');

function createMapsRoutes(mapsController, authMiddleware, rateLimitMiddleware) {
  const router = express.Router();

  // Validation rules
  const distanceMatrixValidation = [
    body('origins')
      .notEmpty()
      .withMessage('Origins are required')
      .custom((value) => {
        const origins = Array.isArray(value) ? value : [value];
        if (origins.length === 0 || origins.length > 25) {
          throw new Error('Origins must contain 1-25 addresses');
        }
        return true;
      }),
    body('destinations')
      .notEmpty()
      .withMessage('Destinations are required')
      .custom((value) => {
        const destinations = Array.isArray(value) ? value : [value];
        if (destinations.length === 0 || destinations.length > 25) {
          throw new Error('Destinations must contain 1-25 addresses');
        }
        return true;
      }),
    body('options.mode')
      .optional()
      .isIn(['driving', 'walking', 'bicycling', 'transit'])
      .withMessage('Invalid mode'),
    body('options.units')
      .optional()
      .isIn(['metric', 'imperial'])
      .withMessage('Invalid units'),
    body('options.language')
      .optional()
      .isIn(['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'])
      .withMessage('Invalid language'),
    body('options.trafficModel')
      .optional()
      .isIn(['best_guess', 'pessimistic', 'optimistic'])
      .withMessage('Invalid traffic model')
  ];

  const singleDistanceValidation = [
    query('origin')
      .notEmpty()
      .withMessage('Origin is required')
      .isLength({ min: 1, max: 200 })
      .withMessage('Origin must be 1-200 characters'),
    query('destination')
      .notEmpty()
      .withMessage('Destination is required')
      .isLength({ min: 1, max: 200 })
      .withMessage('Destination must be 1-200 characters'),
    query('mode')
      .optional()
      .isIn(['driving', 'walking', 'bicycling', 'transit'])
      .withMessage('Invalid mode'),
    query('units')
      .optional()
      .isIn(['metric', 'imperial'])
      .withMessage('Invalid units'),
    query('language')
      .optional()
      .isIn(['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'])
      .withMessage('Invalid language')
  ];

  // Routes

  /**
   * @swagger
   * /api/maps/info:
   *   get:
   *     summary: Get Google Maps API information
   *     tags: [Maps]
   *     responses:
   *       200:
   *         description: API information retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   */
  router.get('/info',
    rateLimitMiddleware.read(),
    (req, res) => mapsController.getApiInfo(req, res)
  );

  /**
   * @swagger
   * /api/maps/distance:
   *   get:
   *     summary: Get distance between two locations
   *     tags: [Maps]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: origin
   *         required: true
   *         schema:
   *           type: string
   *           maxLength: 200
   *         description: Origin address
   *       - in: query
   *         name: destination
   *         required: true
   *         schema:
   *           type: string
   *           maxLength: 200
   *         description: Destination address
   *       - in: query
   *         name: mode
   *         schema:
   *           type: string
   *           enum: [driving, walking, bicycling, transit]
   *           default: driving
   *         description: Travel mode
   *       - in: query
   *         name: units
   *         schema:
   *           type: string
   *           enum: [metric, imperial]
   *           default: metric
   *         description: Unit system
   *       - in: query
   *         name: language
   *         schema:
   *           type: string
   *           enum: [en, es, fr, de, it, pt, ru, ja, ko, zh]
   *           default: en
   *         description: Language for results
   *     responses:
   *       200:
   *         description: Distance calculated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *       400:
   *         description: Invalid request parameters
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       401:
   *         description: Authentication required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       429:
   *         description: Rate limit exceeded
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.get('/distance',
    rateLimitMiddleware.externalApi(),
    authMiddleware.authenticate(),
    singleDistanceValidation,
    validationMiddleware,
    (req, res) => mapsController.getDistance(req, res)
  );

  /**
   * @swagger
   * /api/maps/distance-matrix:
   *   post:
   *     summary: Get distance matrix for multiple origins and destinations
   *     tags: [Maps]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - origins
   *               - destinations
   *             properties:
   *               origins:
   *                 type: array
   *                 items:
   *                   type: string
   *                 minItems: 1
   *                 maxItems: 25
   *                 description: Array of origin addresses
   *               destinations:
   *                 type: array
   *                 items:
   *                   type: string
   *                 minItems: 1
   *                 maxItems: 25
   *                 description: Array of destination addresses
   *               options:
   *                 type: object
   *                 properties:
   *                   mode:
   *                     type: string
   *                     enum: [driving, walking, bicycling, transit]
   *                     default: driving
   *                   units:
   *                     type: string
   *                     enum: [metric, imperial]
   *                     default: metric
   *                   language:
   *                     type: string
   *                     enum: [en, es, fr, de, it, pt, ru, ja, ko, zh]
   *                     default: en
   *                   avoid:
   *                     type: array
   *                     items:
   *                       type: string
   *                       enum: [tolls, highways, ferries, indoor]
   *                   trafficModel:
   *                     type: string
   *                     enum: [best_guess, pessimistic, optimistic]
   *                   departureTime:
   *                     type: integer
   *                     description: Departure time as Unix timestamp
   *                   arrivalTime:
   *                     type: integer
   *                     description: Arrival time as Unix timestamp
   *     responses:
   *       200:
   *         description: Distance matrix calculated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *       400:
   *         description: Invalid request parameters
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       401:
   *         description: Authentication required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       429:
   *         description: Rate limit exceeded
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.post('/distance-matrix',
    rateLimitMiddleware.externalApi(),
    authMiddleware.authenticate(),
    distanceMatrixValidation,
    validationMiddleware,
    (req, res) => mapsController.getDistanceMatrix(req, res)
  );

  /**
   * @swagger
   * /api/maps/cache/clear:
   *   delete:
   *     summary: Clear Google Maps API cache
   *     tags: [Maps]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: pattern
   *         schema:
   *           type: string
   *           default: google_maps:*
   *         description: Cache key pattern to clear
   *     responses:
   *       200:
   *         description: Cache cleared successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *       401:
   *         description: Authentication required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       403:
   *         description: Admin access required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.delete('/cache/clear',
    rateLimitMiddleware.write(),
    authMiddleware.authenticate(),
    authMiddleware.authorize(['admin']),
    (req, res) => mapsController.clearCache(req, res)
  );

  return router;
}

module.exports = createMapsRoutes;
