/**
 * Report Routes
 * Defines HTTP routes for reporting and statistics operations
 */
const express = require('express');
const { query } = require('express-validator');
const validationMiddleware = require('../middleware/validationMiddleware');

function createReportRoutes(reportController, authMiddleware, rateLimitMiddleware) {
  const router = express.Router();

  // Validation rules
  const dateRangeValidation = [
    query('startDate')
      .notEmpty()
      .withMessage('Start date is required')
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate')
      .notEmpty()
      .withMessage('End date is required')
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
  ];

  const optionalDateRangeValidation = [
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
  ];

  const paginationValidation = [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ];

  const formatValidation = [
    query('format')
      .optional()
      .isIn(['json', 'csv'])
      .withMessage('Format must be json or csv')
  ];

  // Routes

  /**
   * @swagger
   * /api/reports/events/statistics:
   *   get:
   *     summary: Get event statistics
   *     tags: [Reports]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: startDate
   *         required: true
   *         schema:
   *           type: string
   *           format: date
   *         description: Start date for statistics (ISO 8601 format)
   *       - in: query
   *         name: endDate
   *         required: true
   *         schema:
   *           type: string
   *           format: date
   *         description: End date for statistics (ISO 8601 format)
   *       - in: query
   *         name: groupBy
   *         schema:
   *           type: string
   *           enum: [day, week, month, year]
   *           default: day
   *         description: Group statistics by time period
   *     responses:
   *       200:
   *         description: Event statistics retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *       400:
   *         description: Invalid request parameters
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       401:
   *         description: Authentication required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.get('/events/statistics',
    rateLimitMiddleware.read(),
    authMiddleware.authenticate(),
    authMiddleware.authorize(['admin', 'organizer']),
    dateRangeValidation,
    query('groupBy')
      .optional()
      .isIn(['day', 'week', 'month', 'year'])
      .withMessage('Group by must be day, week, month, or year'),
    validationMiddleware,
    (req, res) => reportController.getEventStatistics(req, res)
  );

  /**
   * @swagger
   * /api/reports/events:
   *   get:
   *     summary: Get event report
   *     tags: [Reports]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: startDate
   *         schema:
   *           type: string
   *           format: date
   *         description: Start date filter (ISO 8601 format)
   *       - in: query
   *         name: endDate
   *         schema:
   *           type: string
   *           format: date
   *         description: End date filter (ISO 8601 format)
   *       - in: query
   *         name: status
   *         schema:
   *           type: string
   *           enum: [draft, published, cancelled, completed]
   *         description: Filter by event status
   *       - in: query
   *         name: organizerId
   *         schema:
   *           type: string
   *         description: Filter by organizer ID
   *       - in: query
   *         name: format
   *         schema:
   *           type: string
   *           enum: [json, csv]
   *           default: json
   *         description: Response format
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 50
   *         description: Number of items per page
   *     responses:
   *       200:
   *         description: Event report retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *           text/csv:
   *             schema:
   *               type: string
   *       400:
   *         description: Invalid request parameters
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       401:
   *         description: Authentication required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.get('/events',
    rateLimitMiddleware.read(),
    authMiddleware.authenticate(),
    authMiddleware.authorize(['admin', 'organizer']),
    optionalDateRangeValidation,
    query('status')
      .optional()
      .isIn(['draft', 'published', 'cancelled', 'completed'])
      .withMessage('Status must be draft, published, cancelled, or completed'),
    query('organizerId')
      .optional()
      .isMongoId()
      .withMessage('Organizer ID must be a valid MongoDB ObjectId'),
    formatValidation,
    paginationValidation,
    validationMiddleware,
    (req, res) => reportController.getEventReport(req, res)
  );

  /**
   * @swagger
   * /api/reports/attendees:
   *   get:
   *     summary: Get attendee report
   *     tags: [Reports]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: startDate
   *         schema:
   *           type: string
   *           format: date
   *         description: Start date filter (ISO 8601 format)
   *       - in: query
   *         name: endDate
   *         schema:
   *           type: string
   *           format: date
   *         description: End date filter (ISO 8601 format)
   *       - in: query
   *         name: eventId
   *         schema:
   *           type: string
   *         description: Filter by specific event ID
   *       - in: query
   *         name: format
   *         schema:
   *           type: string
   *           enum: [json, csv]
   *           default: json
   *         description: Response format
   *     responses:
   *       200:
   *         description: Attendee report retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *           text/csv:
   *             schema:
   *               type: string
   *       400:
   *         description: Invalid request parameters
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       401:
   *         description: Authentication required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       404:
   *         description: Event not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.get('/attendees',
    rateLimitMiddleware.read(),
    authMiddleware.authenticate(),
    authMiddleware.authorize(['admin', 'organizer']),
    optionalDateRangeValidation,
    query('eventId')
      .optional()
      .isMongoId()
      .withMessage('Event ID must be a valid MongoDB ObjectId'),
    formatValidation,
    validationMiddleware,
    (req, res) => reportController.getAttendeeReport(req, res)
  );

  /**
   * @swagger
   * /api/reports/organizers:
   *   get:
   *     summary: Get organizer report
   *     tags: [Reports]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: startDate
   *         required: true
   *         schema:
   *           type: string
   *           format: date
   *         description: Start date for report (ISO 8601 format)
   *       - in: query
   *         name: endDate
   *         required: true
   *         schema:
   *           type: string
   *           format: date
   *         description: End date for report (ISO 8601 format)
   *       - in: query
   *         name: format
   *         schema:
   *           type: string
   *           enum: [json, csv]
   *           default: json
   *         description: Response format
   *     responses:
   *       200:
   *         description: Organizer report retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *           text/csv:
   *             schema:
   *               type: string
   *       400:
   *         description: Invalid request parameters
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       401:
   *         description: Authentication required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.get('/organizers',
    rateLimitMiddleware.read(),
    authMiddleware.authenticate(),
    authMiddleware.authorize(['admin']), // Only admins can see organizer reports
    dateRangeValidation,
    formatValidation,
    validationMiddleware,
    (req, res) => reportController.getOrganizerReport(req, res)
  );

  return router;
}

module.exports = createReportRoutes;
