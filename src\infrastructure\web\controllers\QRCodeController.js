/**
 * QR Code Controller
 * Handles HTTP requests for QR code generation
 */
const QRCode = require('qrcode');

class QRCodeController {
  constructor(getEventsUseCase) {
    this.getEventsUseCase = getEventsUseCase;
  }

  async generateEventQR(req, res) {
    try {
      const eventId = req.params.id;
      const format = req.query.format || 'png';
      const size = parseInt(req.query.size) || 200;

      // Validate format
      if (!['png', 'svg', 'pdf'].includes(format)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid format. Supported formats: png, svg, pdf'
        });
      }

      // Validate size
      if (size < 100 || size > 1000) {
        return res.status(400).json({
          success: false,
          message: 'Size must be between 100 and 1000 pixels'
        });
      }

      // Get event details
      const event = await this.getEventsUseCase.executeById(eventId);

      // Create QR code data
      const qrData = {
        type: 'event',
        id: event.id,
        title: event.title,
        startDateTime: event.startDateTime,
        endDateTime: event.endDateTime,
        location: event.location,
        url: `${req.protocol}://${req.get('host')}/api/events/${eventId}`
      };

      const qrString = JSON.stringify(qrData);

      // QR Code options
      const options = {
        width: size,
        height: size,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      };

      if (format === 'png') {
        // Generate PNG QR code
        const qrCodeBuffer = await QRCode.toBuffer(qrString, {
          ...options,
          type: 'png'
        });

        res.set({
          'Content-Type': 'image/png',
          'Content-Disposition': `inline; filename="event-${eventId}-qr.png"`
        });

        res.send(qrCodeBuffer);
      } else if (format === 'svg') {
        // Generate SVG QR code
        const qrCodeSvg = await QRCode.toString(qrString, {
          ...options,
          type: 'svg'
        });

        res.set({
          'Content-Type': 'image/svg+xml',
          'Content-Disposition': `inline; filename="event-${eventId}-qr.svg"`
        });

        res.send(qrCodeSvg);
      } else if (format === 'pdf') {
        // For PDF, we'll return the data URL that can be embedded
        const qrCodeDataURL = await QRCode.toDataURL(qrString, options);

        res.json({
          success: true,
          data: {
            eventId: event.id,
            eventTitle: event.title,
            qrCodeDataURL,
            format: 'pdf-data-url',
            instructions: 'Use this data URL to embed the QR code in a PDF document'
          }
        });
      }
    } catch (error) {
      const statusCode = error.message === 'Event not found' ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  async generateCustomQR(req, res) {
    try {
      const { data, format = 'png', size = 200 } = req.body;

      if (!data) {
        return res.status(400).json({
          success: false,
          message: 'Data is required for QR code generation'
        });
      }

      // Validate format
      if (!['png', 'svg', 'json'].includes(format)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid format. Supported formats: png, svg, json'
        });
      }

      // Validate size
      if (size < 100 || size > 1000) {
        return res.status(400).json({
          success: false,
          message: 'Size must be between 100 and 1000 pixels'
        });
      }

      // QR Code options
      const options = {
        width: size,
        height: size,
        margin: 2,
        color: {
          dark: req.body.darkColor || '#000000',
          light: req.body.lightColor || '#FFFFFF'
        },
        errorCorrectionLevel: req.body.errorCorrectionLevel || 'M'
      };

      if (format === 'png') {
        const qrCodeBuffer = await QRCode.toBuffer(data, {
          ...options,
          type: 'png'
        });

        res.set({
          'Content-Type': 'image/png',
          'Content-Disposition': 'inline; filename="custom-qr.png"'
        });

        res.send(qrCodeBuffer);
      } else if (format === 'svg') {
        const qrCodeSvg = await QRCode.toString(data, {
          ...options,
          type: 'svg'
        });

        res.set({
          'Content-Type': 'image/svg+xml',
          'Content-Disposition': 'inline; filename="custom-qr.svg"'
        });

        res.send(qrCodeSvg);
      } else if (format === 'json') {
        const qrCodeDataURL = await QRCode.toDataURL(data, options);

        res.json({
          success: true,
          data: {
            qrCodeDataURL,
            format: 'data-url',
            size,
            options
          }
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getQRInfo(req, res) {
    try {
      res.json({
        success: true,
        data: {
          supportedFormats: ['png', 'svg', 'pdf'],
          sizeRange: {
            min: 100,
            max: 1000,
            default: 200
          },
          errorCorrectionLevels: ['L', 'M', 'Q', 'H'],
          defaultErrorCorrectionLevel: 'M',
          maxDataLength: 2953, // For QR Code version 40 with error correction level L
          examples: {
            eventQR: '/api/qr/event/{eventId}?format=png&size=300',
            customQR: '/api/qr/custom (POST with data in body)'
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
}

module.exports = QRCodeController;
