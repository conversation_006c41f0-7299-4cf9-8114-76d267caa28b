/**
 * Register User Use Case
 * Application layer use case for user registration
 */
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const User = require('../../domain/entities/User');

class RegisterUserUseCase {
  constructor(userRepository, config) {
    this.userRepository = userRepository;
    this.config = config;
  }

  async execute(userData) {
    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(userData.email.toLowerCase());
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Create user entity and validate
    const user = new User({
      ...userData,
      email: userData.email.toLowerCase().trim(),
      firstName: userData.firstName.trim(),
      lastName: userData.lastName.trim(),
      password: userData.password
    });

    user.validate();

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 10);
    user.password = hashedPassword;

    // Create user in repository
    const createdUser = await this.userRepository.create(user);

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: createdUser.id,
        email: createdUser.email,
        role: createdUser.role
      },
      this.config.jwtSecret,
      {
        expiresIn: this.config.jwtExpiresIn || '24h'
      }
    );

    // Return user data without password
    const { password: _, ...userWithoutPassword } = createdUser;

    return {
      user: userWithoutPassword,
      token,
      message: 'User registered successfully'
    };
  }
}

module.exports = RegisterUserUseCase;
