/**
 * Register User Use Case
 * Application layer use case for user registration
 */
const bcrypt = require('bcrypt');
const User = require('../../domain/entities/User');

class RegisterUserUseCase {
  constructor(userRepository, config) {
    this.userRepository = userRepository;
    this.config = config;
  }

  async execute(userData) {
    // Check if user already exists
    const existingUser = await this.userRepository.findByEmail(userData.email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Create user entity and validate
    const user = new User({
      ...userData,
      password: userData.password // Will be hashed by the model
    });
    
    user.validate();

    // Create user in repository
    const createdUser = await this.userRepository.create(user);

    // Return user data without password
    const { password: _, ...userWithoutPassword } = createdUser;
    
    return {
      user: userWithoutPassword,
      message: 'User registered successfully'
    };
  }
}

module.exports = RegisterUserUseCase;
