/**
 * Unit tests for RegisterUserUseCase
 */
const RegisterUserUseCase = require('../../../src/application/usecases/RegisterUserUseCase');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

// Mock dependencies
jest.mock('bcrypt');
jest.mock('jsonwebtoken');

describe('RegisterUserUseCase', () => {
  let registerUserUseCase;
  let mockUserRepository;
  let mockConfig;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock user repository
    mockUserRepository = {
      findByEmail: jest.fn(),
      create: jest.fn()
    };

    // Mock config
    mockConfig = {
      jwtSecret: 'test-secret',
      jwtExpiresIn: '1h'
    };

    registerUserUseCase = new RegisterUserUseCase(mockUserRepository, mockConfig);
  });

  describe('execute', () => {
    const validUserData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'John',
      lastName: 'Doe'
    };

    const hashedPassword = '$2b$10$hashedpassword';
    const createdUser = {
      id: 'user123',
      email: validUserData.email,
      firstName: validUserData.firstName,
      lastName: validUserData.lastName,
      role: 'user',
      status: 'active',
      createdAt: new Date()
    };

    it('should successfully register a new user', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null); // User doesn't exist
      bcrypt.hash.mockResolvedValue(hashedPassword);
      mockUserRepository.create.mockResolvedValue(createdUser);
      jwt.sign.mockReturnValue('mock-jwt-token');

      // Act
      const result = await registerUserUseCase.execute(validUserData);

      // Assert
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validUserData.email.toLowerCase());
      expect(bcrypt.hash).toHaveBeenCalledWith(validUserData.password, 10);
      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: validUserData.email,
        password: hashedPassword,
        firstName: validUserData.firstName,
        lastName: validUserData.lastName,
        role: 'user',
        status: 'active'
      });
      expect(jwt.sign).toHaveBeenCalledWith(
        {
          userId: createdUser.id,
          email: createdUser.email,
          role: createdUser.role
        },
        mockConfig.jwtSecret,
        { expiresIn: mockConfig.jwtExpiresIn }
      );

      expect(result).toEqual({
        user: {
          id: createdUser.id,
          email: createdUser.email,
          firstName: createdUser.firstName,
          lastName: createdUser.lastName,
          role: createdUser.role,
          status: createdUser.status,
          createdAt: createdUser.createdAt
        },
        token: 'mock-jwt-token',
        message: 'User registered successfully'
      });
    });

    it('should throw error when user already exists', async () => {
      // Arrange
      const existingUser = { id: 'existing123', email: validUserData.email };
      mockUserRepository.findByEmail.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(registerUserUseCase.execute(validUserData))
        .rejects.toThrow('User with this email already exists');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validUserData.email);
      expect(bcrypt.hash).not.toHaveBeenCalled();
      expect(mockUserRepository.create).not.toHaveBeenCalled();
      expect(jwt.sign).not.toHaveBeenCalled();
    });

    it('should throw error with invalid email format', async () => {
      // Arrange
      const invalidUserData = { ...validUserData, email: 'invalid-email' };

      // Act & Assert
      await expect(registerUserUseCase.execute(invalidUserData))
        .rejects.toThrow('Invalid email format');

      expect(mockUserRepository.findByEmail).not.toHaveBeenCalled();
    });

    it('should throw error with weak password', async () => {
      // Arrange
      const weakPasswordData = { ...validUserData, password: '123' };

      // Act & Assert
      await expect(registerUserUseCase.execute(weakPasswordData))
        .rejects.toThrow('Password must be at least 6 characters long');

      expect(mockUserRepository.findByEmail).not.toHaveBeenCalled();
    });

    it('should throw error with missing required fields', async () => {
      // Test missing email
      await expect(registerUserUseCase.execute({ ...validUserData, email: '' }))
        .rejects.toThrow('Email is required');

      // Test missing password
      await expect(registerUserUseCase.execute({ ...validUserData, password: '' }))
        .rejects.toThrow('Password is required');

      // Test missing firstName
      await expect(registerUserUseCase.execute({ ...validUserData, firstName: '' }))
        .rejects.toThrow('First name is required');

      // Test missing lastName
      await expect(registerUserUseCase.execute({ ...validUserData, lastName: '' }))
        .rejects.toThrow('Last name is required');
    });

    it('should trim whitespace from input fields', async () => {
      // Arrange
      const userDataWithWhitespace = {
        email: '  <EMAIL>  ',
        password: 'password123',
        firstName: '  John  ',
        lastName: '  Doe  '
      };

      mockUserRepository.findByEmail.mockResolvedValue(null);
      bcrypt.hash.mockResolvedValue(hashedPassword);
      mockUserRepository.create.mockResolvedValue(createdUser);
      jwt.sign.mockReturnValue('mock-jwt-token');

      // Act
      await registerUserUseCase.execute(userDataWithWhitespace);

      // Assert
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'John',
        lastName: 'Doe',
        role: 'user',
        status: 'active'
      });
    });

    it('should convert email to lowercase', async () => {
      // Arrange
      const upperCaseEmailData = { ...validUserData, email: '<EMAIL>' };
      mockUserRepository.findByEmail.mockResolvedValue(null);
      bcrypt.hash.mockResolvedValue(hashedPassword);
      mockUserRepository.create.mockResolvedValue(createdUser);
      jwt.sign.mockReturnValue('mock-jwt-token');

      // Act
      await registerUserUseCase.execute(upperCaseEmailData);

      // Assert
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith('<EMAIL>');
      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: validUserData.firstName,
        lastName: validUserData.lastName,
        role: 'user',
        status: 'active'
      });
    });

    it('should handle repository errors during user lookup', async () => {
      // Arrange
      const repositoryError = new Error('Database connection failed');
      mockUserRepository.findByEmail.mockRejectedValue(repositoryError);

      // Act & Assert
      await expect(registerUserUseCase.execute(validUserData))
        .rejects.toThrow('Database connection failed');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validUserData.email);
    });

    it('should handle bcrypt hashing errors', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null);
      const bcryptError = new Error('Bcrypt hashing failed');
      bcrypt.hash.mockRejectedValue(bcryptError);

      // Act & Assert
      await expect(registerUserUseCase.execute(validUserData))
        .rejects.toThrow('Bcrypt hashing failed');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validUserData.email);
      expect(bcrypt.hash).toHaveBeenCalledWith(validUserData.password, 10);
    });

    it('should handle repository errors during user creation', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null);
      bcrypt.hash.mockResolvedValue(hashedPassword);
      const createError = new Error('User creation failed');
      mockUserRepository.create.mockRejectedValue(createError);

      // Act & Assert
      await expect(registerUserUseCase.execute(validUserData))
        .rejects.toThrow('User creation failed');

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(validUserData.email);
      expect(bcrypt.hash).toHaveBeenCalledWith(validUserData.password, 10);
      expect(mockUserRepository.create).toHaveBeenCalled();
    });

    it('should handle JWT signing errors', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null);
      bcrypt.hash.mockResolvedValue(hashedPassword);
      mockUserRepository.create.mockResolvedValue(createdUser);
      const jwtError = new Error('JWT signing failed');
      jwt.sign.mockImplementation(() => {
        throw jwtError;
      });

      // Act & Assert
      await expect(registerUserUseCase.execute(validUserData))
        .rejects.toThrow('JWT signing failed');

      expect(jwt.sign).toHaveBeenCalled();
    });

    it('should not include password in returned user object', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null);
      bcrypt.hash.mockResolvedValue(hashedPassword);
      mockUserRepository.create.mockResolvedValue(createdUser);
      jwt.sign.mockReturnValue('mock-jwt-token');

      // Act
      const result = await registerUserUseCase.execute(validUserData);

      // Assert
      expect(result.user).not.toHaveProperty('password');
      expect(result.user).toEqual({
        id: createdUser.id,
        email: createdUser.email,
        firstName: createdUser.firstName,
        lastName: createdUser.lastName,
        role: createdUser.role
      });
    });
  });
});
