/**
 * Integration tests for Authentication API
 * Tests the complete authentication flow including HTTP endpoints
 */
const request = require('supertest');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

// Mock the Application class and its dependencies
jest.mock('../../src/infrastructure/database/connection');
jest.mock('../../src/infrastructure/cache/RedisService');

const Application = require('../../src/app');

describe('Authentication API Integration Tests', () => {
  let app;
  let server;
  let mockUserRepository;
  let mockConfig;

  beforeAll(async () => {
    // Mock configuration
    mockConfig = {
      port: 3001,
      mongodb: { uri: 'mongodb://localhost:27017/test' },
      redis: { url: 'redis://localhost:6379/1' },
      jwt: { secret: 'test-secret', expiresIn: '1h' },
      email: { host: 'smtp.test.com', port: 587, user: 'test', pass: 'test' },
      googleMaps: { apiKey: 'test-key' },
      rateLimit: { windowMs: 900000, max: 100 }
    };

    // Create application instance
    const application = new Application(mockConfig);
    app = application.getApp();
    
    // Mock the user repository
    mockUserRepository = {
      findByEmail: jest.fn(),
      findById: jest.fn(),
      create: jest.fn()
    };

    // Replace the repository in the application
    application.userRepository = mockUserRepository;
    application.authController.authenticateUserUseCase.userRepository = mockUserRepository;
    application.authController.registerUserUseCase.userRepository = mockUserRepository;
    application.authController.getUserUseCase.userRepository = mockUserRepository;
    application.authMiddleware.userRepository = mockUserRepository;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/login', () => {
    const loginEndpoint = '/api/auth/login';
    const validLoginData = {
      email: '<EMAIL>',
      password: 'password123'
    };

    it('should successfully login with valid credentials', async () => {
      // Arrange
      const hashedPassword = await bcrypt.hash(validLoginData.password, 10);
      const mockUser = {
        id: 'user123',
        email: validLoginData.email,
        password: hashedPassword,
        firstName: 'John',
        lastName: 'Doe',
        role: 'user',
        status: 'active'
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);

      // Act
      const response = await request(app)
        .post(loginEndpoint)
        .send(validLoginData)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Login successful');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
        role: mockUser.role
      });

      // Verify JWT token
      const decodedToken = jwt.verify(response.body.data.token, mockConfig.jwt.secret);
      expect(decodedToken.userId).toBe(mockUser.id);
      expect(decodedToken.email).toBe(mockUser.email);
      expect(decodedToken.role).toBe(mockUser.role);
    });

    it('should return 401 for invalid email', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null);

      // Act
      const response = await request(app)
        .post(loginEndpoint)
        .send(validLoginData)
        .expect(401);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid email or password');
      expect(response.body.data).toBeUndefined();
    });

    it('should return 401 for invalid password', async () => {
      // Arrange
      const hashedPassword = await bcrypt.hash('differentpassword', 10);
      const mockUser = {
        id: 'user123',
        email: validLoginData.email,
        password: hashedPassword,
        status: 'active'
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);

      // Act
      const response = await request(app)
        .post(loginEndpoint)
        .send(validLoginData)
        .expect(401);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid email or password');
    });

    it('should return 403 for inactive account', async () => {
      // Arrange
      const hashedPassword = await bcrypt.hash(validLoginData.password, 10);
      const mockUser = {
        id: 'user123',
        email: validLoginData.email,
        password: hashedPassword,
        status: 'inactive'
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);

      // Act
      const response = await request(app)
        .post(loginEndpoint)
        .send(validLoginData)
        .expect(403);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Account is inactive');
    });

    it('should return 403 for suspended account', async () => {
      // Arrange
      const hashedPassword = await bcrypt.hash(validLoginData.password, 10);
      const mockUser = {
        id: 'user123',
        email: validLoginData.email,
        password: hashedPassword,
        status: 'suspended'
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);

      // Act
      const response = await request(app)
        .post(loginEndpoint)
        .send(validLoginData)
        .expect(403);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Account is suspended');
    });

    it('should return 400 for missing email', async () => {
      // Act
      const response = await request(app)
        .post(loginEndpoint)
        .send({ password: 'password123' })
        .expect(400);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Email is required');
    });

    it('should return 400 for missing password', async () => {
      // Act
      const response = await request(app)
        .post(loginEndpoint)
        .send({ email: '<EMAIL>' })
        .expect(400);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('Password is required');
    });

    it('should return 400 for invalid email format', async () => {
      // Act
      const response = await request(app)
        .post(loginEndpoint)
        .send({ email: 'invalid-email', password: 'password123' })
        .expect(400);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('must be a valid email');
    });
  });

  describe('POST /api/auth/register', () => {
    const registerEndpoint = '/api/auth/register';
    const validRegisterData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Jane',
      lastName: 'Smith'
    };

    it('should successfully register a new user', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null); // User doesn't exist
      const createdUser = {
        id: 'newuser123',
        email: validRegisterData.email,
        firstName: validRegisterData.firstName,
        lastName: validRegisterData.lastName,
        role: 'user',
        status: 'active'
      };
      mockUserRepository.create.mockResolvedValue(createdUser);

      // Act
      const response = await request(app)
        .post(registerEndpoint)
        .send(validRegisterData)
        .expect(201);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('User registered successfully');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user).toEqual({
        id: createdUser.id,
        email: createdUser.email,
        firstName: createdUser.firstName,
        lastName: createdUser.lastName,
        role: createdUser.role
      });

      // Verify JWT token
      const decodedToken = jwt.verify(response.body.data.token, mockConfig.jwt.secret);
      expect(decodedToken.userId).toBe(createdUser.id);
      expect(decodedToken.email).toBe(createdUser.email);
      expect(decodedToken.role).toBe(createdUser.role);
    });

    it('should return 400 for existing user', async () => {
      // Arrange
      const existingUser = { id: 'existing123', email: validRegisterData.email };
      mockUserRepository.findByEmail.mockResolvedValue(existingUser);

      // Act
      const response = await request(app)
        .post(registerEndpoint)
        .send(validRegisterData)
        .expect(400);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('User with this email already exists');
    });

    it('should return 400 for missing required fields', async () => {
      // Test missing email
      let response = await request(app)
        .post(registerEndpoint)
        .send({ ...validRegisterData, email: '' })
        .expect(400);
      expect(response.body.message).toContain('Email is required');

      // Test missing password
      response = await request(app)
        .post(registerEndpoint)
        .send({ ...validRegisterData, password: '' })
        .expect(400);
      expect(response.body.message).toContain('Password is required');

      // Test missing firstName
      response = await request(app)
        .post(registerEndpoint)
        .send({ ...validRegisterData, firstName: '' })
        .expect(400);
      expect(response.body.message).toContain('First name is required');

      // Test missing lastName
      response = await request(app)
        .post(registerEndpoint)
        .send({ ...validRegisterData, lastName: '' })
        .expect(400);
      expect(response.body.message).toContain('Last name is required');
    });

    it('should return 400 for weak password', async () => {
      // Act
      const response = await request(app)
        .post(registerEndpoint)
        .send({ ...validRegisterData, password: '123' })
        .expect(400);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('at least 6 characters');
    });
  });

  describe('GET /api/auth/profile', () => {
    const profileEndpoint = '/api/auth/profile';

    it('should return user profile with valid token', async () => {
      // Arrange
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        role: 'user',
        status: 'active'
      };

      const token = jwt.sign(
        { userId: mockUser.id, email: mockUser.email, role: mockUser.role },
        mockConfig.jwt.secret,
        { expiresIn: '1h' }
      );

      mockUserRepository.findById.mockResolvedValue(mockUser);

      // Act
      const response = await request(app)
        .get(profileEndpoint)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
        role: mockUser.role,
        status: mockUser.status
      });
    });

    it('should return 401 without token', async () => {
      // Act
      const response = await request(app)
        .get(profileEndpoint)
        .expect(401);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Access token is required');
    });

    it('should return 401 with invalid token', async () => {
      // Act
      const response = await request(app)
        .get(profileEndpoint)
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid or expired token');
    });

    it('should return 401 with expired token', async () => {
      // Arrange
      const expiredToken = jwt.sign(
        { userId: 'user123', email: '<EMAIL>', role: 'user' },
        mockConfig.jwt.secret,
        { expiresIn: '-1h' } // Expired 1 hour ago
      );

      // Act
      const response = await request(app)
        .get(profileEndpoint)
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid or expired token');
    });

    it('should return 401 when user not found', async () => {
      // Arrange
      const token = jwt.sign(
        { userId: 'nonexistent', email: '<EMAIL>', role: 'user' },
        mockConfig.jwt.secret,
        { expiresIn: '1h' }
      );

      mockUserRepository.findById.mockResolvedValue(null);

      // Act
      const response = await request(app)
        .get(profileEndpoint)
        .set('Authorization', `Bearer ${token}`)
        .expect(401);

      // Assert
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('User not found');
    });
  });
});
