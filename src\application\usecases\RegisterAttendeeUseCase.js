/**
 * Register Attendee Use Case
 * Application layer use case for registering attendees to events
 */
class RegisterAttendeeUseCase {
  constructor(eventRepository, userRepository, emailService) {
    this.eventRepository = eventRepository;
    this.userRepository = userRepository;
    this.emailService = emailService;
  }

  async execute(eventId, attendeeEmail) {
    // Get the event
    const event = await this.eventRepository.findById(eventId);
    if (!event) {
      throw new Error('Event not found');
    }

    // Validate event status
    if (event.status !== 'published') {
      throw new Error('Event is not available for registration');
    }

    // Check if event has already started
    if (event.startDateTime <= new Date()) {
      throw new Error('Cannot register for an event that has already started');
    }

    // Validate email format
    if (!this.isValidEmail(attendeeEmail)) {
      throw new Error('Invalid email address');
    }

    // Check if attendee is already registered
    const isAlreadyRegistered = event.attendees.some(
      attendee => attendee.email.toLowerCase() === attendeeEmail.toLowerCase()
    );

    if (isAlreadyRegistered) {
      throw new Error('You are already registered for this event');
    }

    // Check capacity
    if (event.maxAttendees && event.attendees.length >= event.maxAttendees) {
      throw new Error('Event has reached maximum capacity');
    }

    // Get user information if the email belongs to a registered user
    const user = await this.userRepository.findByEmail(attendeeEmail);
    
    // Create attendee object
    const attendee = {
      email: attendeeEmail,
      registeredAt: new Date(),
      status: 'registered'
    };

    // Add user information if available
    if (user) {
      attendee.userId = user.id;
      attendee.firstName = user.firstName;
      attendee.lastName = user.lastName;
    }

    // Add attendee to event
    event.attendees.push(attendee);

    // Update the event
    const updatedEvent = await this.eventRepository.update(eventId, {
      attendees: event.attendees
    });

    // Send confirmation email
    await this.sendRegistrationConfirmation(attendeeEmail, updatedEvent, attendee);

    return {
      event: updatedEvent,
      attendee: attendee,
      message: 'Successfully registered for the event'
    };
  }

  async sendRegistrationConfirmation(email, event, attendee) {
    if (!this.emailService) {
      return;
    }

    try {
      await this.emailService.sendEventRegistrationConfirmation(
        email,
        event,
        attendee
      );
    } catch (error) {
      // Log error but don't fail the registration
      console.error('Failed to send registration confirmation:', error);
    }
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  async bulkRegister(eventId, attendeeEmails) {
    const results = {
      successful: [],
      failed: []
    };

    for (const email of attendeeEmails) {
      try {
        const result = await this.execute(eventId, email);
        results.successful.push({
          email: email,
          attendee: result.attendee
        });
      } catch (error) {
        results.failed.push({
          email: email,
          error: error.message
        });
      }
    }

    return results;
  }

  async registerWithWaitlist(eventId, attendeeEmail) {
    // Get the event
    const event = await this.eventRepository.findById(eventId);
    if (!event) {
      throw new Error('Event not found');
    }

    // Try normal registration first
    try {
      return await this.execute(eventId, attendeeEmail);
    } catch (error) {
      // If capacity is full, add to waitlist
      if (error.message === 'Event has reached maximum capacity') {
        return await this.addToWaitlist(eventId, attendeeEmail);
      }
      throw error;
    }
  }

  async addToWaitlist(eventId, attendeeEmail) {
    const event = await this.eventRepository.findById(eventId);
    
    // Initialize waitlist if it doesn't exist
    if (!event.waitlist) {
      event.waitlist = [];
    }

    // Check if already on waitlist
    const isOnWaitlist = event.waitlist.some(
      attendee => attendee.email.toLowerCase() === attendeeEmail.toLowerCase()
    );

    if (isOnWaitlist) {
      throw new Error('You are already on the waitlist for this event');
    }

    // Add to waitlist
    const waitlistEntry = {
      email: attendeeEmail,
      addedAt: new Date(),
      position: event.waitlist.length + 1
    };

    event.waitlist.push(waitlistEntry);

    // Update the event
    const updatedEvent = await this.eventRepository.update(eventId, {
      waitlist: event.waitlist
    });

    // Send waitlist confirmation
    if (this.emailService) {
      try {
        await this.emailService.sendWaitlistConfirmation(
          attendeeEmail,
          updatedEvent,
          waitlistEntry
        );
      } catch (error) {
        console.error('Failed to send waitlist confirmation:', error);
      }
    }

    return {
      event: updatedEvent,
      waitlistEntry: waitlistEntry,
      message: `Added to waitlist at position ${waitlistEntry.position}`
    };
  }
}

module.exports = RegisterAttendeeUseCase;
