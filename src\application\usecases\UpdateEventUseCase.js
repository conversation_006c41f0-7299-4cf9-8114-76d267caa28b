/**
 * Update Event Use Case
 * Application layer use case for updating events
 */
class UpdateEventUseCase {
  constructor(eventRepository, eventService, userRepository, emailService) {
    this.eventRepository = eventRepository;
    this.eventService = eventService;
    this.userRepository = userRepository;
    this.emailService = emailService;
  }

  async execute(eventId, eventData, userId) {
    // Get existing event
    const existingEvent = await this.eventRepository.findById(eventId);
    if (!existingEvent) {
      throw new Error('Event not found');
    }

    // Check if user has permission to update this event
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Only admin, or the event organizer can update the event
    if (user.role !== 'admin' && existingEvent.organizerId !== userId) {
      throw new Error('You do not have permission to update this event');
    }

    // Validate event data
    const validatedData = await this.validateEventData(eventData, existingEvent);

    // Check for schedule conflicts if dates are being changed
    if (validatedData.startDateTime || validatedData.endDateTime) {
      const startDateTime = validatedData.startDateTime || existingEvent.startDateTime;
      const endDateTime = validatedData.endDateTime || existingEvent.endDateTime;
      
      await this.eventService.checkScheduleConflicts(
        existingEvent.organizerId,
        startDateTime,
        endDateTime,
        eventId // Exclude current event from conflict check
      );
    }

    // Update the event
    const updatedEvent = await this.eventRepository.update(eventId, validatedData);

    // Send notification emails to attendees if significant changes were made
    if (this.shouldNotifyAttendees(validatedData, existingEvent)) {
      await this.notifyAttendeesOfChanges(updatedEvent, existingEvent);
    }

    return updatedEvent;
  }

  async validateEventData(eventData, existingEvent) {
    const validatedData = {};

    // Validate title
    if (eventData.title !== undefined) {
      if (!eventData.title || eventData.title.trim().length === 0) {
        throw new Error('Title is required');
      }
      if (eventData.title.length > 200) {
        throw new Error('Title must be less than 200 characters');
      }
      validatedData.title = eventData.title.trim();
    }

    // Validate description
    if (eventData.description !== undefined) {
      if (eventData.description && eventData.description.length > 2000) {
        throw new Error('Description must be less than 2000 characters');
      }
      validatedData.description = eventData.description;
    }

    // Validate dates
    if (eventData.startDateTime !== undefined) {
      const startDate = new Date(eventData.startDateTime);
      if (isNaN(startDate.getTime())) {
        throw new Error('Invalid start date format');
      }
      if (startDate <= new Date()) {
        throw new Error('Start date must be in the future');
      }
      validatedData.startDateTime = startDate;
    }

    if (eventData.endDateTime !== undefined) {
      const endDate = new Date(eventData.endDateTime);
      if (isNaN(endDate.getTime())) {
        throw new Error('Invalid end date format');
      }
      
      const startDateTime = validatedData.startDateTime || existingEvent.startDateTime;
      if (endDate <= startDateTime) {
        throw new Error('End date must be after start date');
      }
      validatedData.endDateTime = endDate;
    }

    // Validate location
    if (eventData.location !== undefined) {
      if (eventData.location && eventData.location.length > 500) {
        throw new Error('Location must be less than 500 characters');
      }
      validatedData.location = eventData.location;
    }

    // Validate max attendees
    if (eventData.maxAttendees !== undefined) {
      if (eventData.maxAttendees !== null) {
        const maxAttendees = parseInt(eventData.maxAttendees);
        if (isNaN(maxAttendees) || maxAttendees < 1) {
          throw new Error('Max attendees must be a positive integer');
        }
        
        // Check if reducing capacity below current attendee count
        if (maxAttendees < existingEvent.attendees.length) {
          throw new Error(`Cannot reduce capacity below current attendee count (${existingEvent.attendees.length})`);
        }
        
        validatedData.maxAttendees = maxAttendees;
      } else {
        validatedData.maxAttendees = null; // Allow unlimited attendees
      }
    }

    // Validate status
    if (eventData.status !== undefined) {
      const validStatuses = ['draft', 'published', 'cancelled', 'completed'];
      if (!validStatuses.includes(eventData.status)) {
        throw new Error(`Invalid status. Valid statuses: ${validStatuses.join(', ')}`);
      }
      validatedData.status = eventData.status;
    }

    return validatedData;
  }

  shouldNotifyAttendees(updatedData, existingEvent) {
    // Notify attendees if significant changes were made
    const significantChanges = [
      'startDateTime',
      'endDateTime',
      'location',
      'status'
    ];

    return significantChanges.some(field => 
      updatedData[field] !== undefined && 
      updatedData[field] !== existingEvent[field]
    );
  }

  async notifyAttendeesOfChanges(updatedEvent, existingEvent) {
    if (!this.emailService || updatedEvent.attendees.length === 0) {
      return;
    }

    try {
      const changes = this.getChangeSummary(updatedEvent, existingEvent);
      
      for (const attendee of updatedEvent.attendees) {
        await this.emailService.sendEventUpdateNotification(
          attendee.email,
          updatedEvent,
          changes
        );
      }
    } catch (error) {
      // Log error but don't fail the update operation
      console.error('Failed to send update notifications:', error);
    }
  }

  getChangeSummary(updatedEvent, existingEvent) {
    const changes = [];

    if (updatedEvent.startDateTime !== existingEvent.startDateTime) {
      changes.push(`Start time changed from ${existingEvent.startDateTime} to ${updatedEvent.startDateTime}`);
    }

    if (updatedEvent.endDateTime !== existingEvent.endDateTime) {
      changes.push(`End time changed from ${existingEvent.endDateTime} to ${updatedEvent.endDateTime}`);
    }

    if (updatedEvent.location !== existingEvent.location) {
      changes.push(`Location changed from "${existingEvent.location}" to "${updatedEvent.location}"`);
    }

    if (updatedEvent.status !== existingEvent.status) {
      changes.push(`Status changed from ${existingEvent.status} to ${updatedEvent.status}`);
    }

    return changes;
  }
}

module.exports = UpdateEventUseCase;
