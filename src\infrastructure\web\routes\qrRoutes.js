/**
 * QR Code Routes
 * Defines HTTP routes for QR code operations
 */
const express = require('express');
const { body, param, query } = require('express-validator');
const validationMiddleware = require('../middleware/validationMiddleware');

function createQRRoutes(qrController, authMiddleware, rateLimitMiddleware) {
  const router = express.Router();

  // Validation rules
  const eventQRValidation = [
    param('id').isMongoId().withMessage('Invalid event ID'),
    query('format')
      .optional()
      .isIn(['png', 'svg', 'pdf'])
      .withMessage('Format must be png, svg, or pdf'),
    query('size')
      .optional()
      .isInt({ min: 100, max: 1000 })
      .withMessage('Size must be between 100 and 1000 pixels')
  ];

  const customQRValidation = [
    body('data')
      .notEmpty()
      .withMessage('Data is required')
      .isLength({ max: 2953 })
      .withMessage('Data is too long for QR code'),
    body('format')
      .optional()
      .isIn(['png', 'svg', 'json'])
      .withMessage('Format must be png, svg, or json'),
    body('size')
      .optional()
      .isInt({ min: 100, max: 1000 })
      .withMessage('Size must be between 100 and 1000 pixels'),
    body('darkColor')
      .optional()
      .matches(/^#[0-9A-F]{6}$/i)
      .withMessage('Dark color must be a valid hex color'),
    body('lightColor')
      .optional()
      .matches(/^#[0-9A-F]{6}$/i)
      .withMessage('Light color must be a valid hex color'),
    body('errorCorrectionLevel')
      .optional()
      .isIn(['L', 'M', 'Q', 'H'])
      .withMessage('Error correction level must be L, M, Q, or H')
  ];

  // Routes

  /**
   * @swagger
   * /api/qr/info:
   *   get:
   *     summary: Get QR code generation information
   *     tags: [QR Code]
   *     responses:
   *       200:
   *         description: QR code information
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   */
  router.get('/info',
    rateLimitMiddleware.read(),
    (req, res) => qrController.getQRInfo(req, res)
  );

  /**
   * @swagger
   * /api/qr/event/{id}:
   *   get:
   *     summary: Generate QR code for an event
   *     tags: [QR Code]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Event ID
   *       - in: query
   *         name: format
   *         schema:
   *           type: string
   *           enum: [png, svg, pdf]
   *           default: png
   *         description: QR code format
   *       - in: query
   *         name: size
   *         schema:
   *           type: integer
   *           minimum: 100
   *           maximum: 1000
   *           default: 200
   *         description: QR code size in pixels
   *     responses:
   *       200:
   *         description: QR code generated successfully
   *         content:
   *           image/png:
   *             schema:
   *               type: string
   *               format: binary
   *           image/svg+xml:
   *             schema:
   *               type: string
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *       400:
   *         description: Invalid request parameters
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       404:
   *         description: Event not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.get('/event/:id',
    rateLimitMiddleware.read(),
    authMiddleware.optional(),
    eventQRValidation,
    validationMiddleware,
    (req, res) => qrController.generateEventQR(req, res)
  );

  /**
   * @swagger
   * /api/qr/custom:
   *   post:
   *     summary: Generate custom QR code
   *     tags: [QR Code]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - data
   *             properties:
   *               data:
   *                 type: string
   *                 maxLength: 2953
   *                 description: Data to encode in QR code
   *               format:
   *                 type: string
   *                 enum: [png, svg, json]
   *                 default: png
   *                 description: QR code format
   *               size:
   *                 type: integer
   *                 minimum: 100
   *                 maximum: 1000
   *                 default: 200
   *                 description: QR code size in pixels
   *               darkColor:
   *                 type: string
   *                 pattern: '^#[0-9A-F]{6}$'
   *                 default: '#000000'
   *                 description: Dark color (hex format)
   *               lightColor:
   *                 type: string
   *                 pattern: '^#[0-9A-F]{6}$'
   *                 default: '#FFFFFF'
   *                 description: Light color (hex format)
   *               errorCorrectionLevel:
   *                 type: string
   *                 enum: [L, M, Q, H]
   *                 default: M
   *                 description: Error correction level
   *     responses:
   *       200:
   *         description: QR code generated successfully
   *         content:
   *           image/png:
   *             schema:
   *               type: string
   *               format: binary
   *           image/svg+xml:
   *             schema:
   *               type: string
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Success'
   *       400:
   *         description: Invalid request parameters
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   *       401:
   *         description: Authentication required
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Error'
   */
  router.post('/custom',
    rateLimitMiddleware.write(),
    authMiddleware.authenticate(),
    customQRValidation,
    validationMiddleware,
    (req, res) => qrController.generateCustomQR(req, res)
  );

  return router;
}

module.exports = createQRRoutes;
