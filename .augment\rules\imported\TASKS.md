---
type: "manual"
---

## **B. PART 2: DESIGN & IMPLEMENT MAIN FEATURES**

### 1. Event Management API
- **CRUD Event API**
  - **Create**: add events, optionally send confirmation emails to attendees after registration.
  - **Read**: search and view event details.
  - **Update**: update event information.
  - **Delete**: delete events.
- Advanced features:
  - Paging
  - Caching
  - Check for schedule conflicts by date/time and end time
  - Send notification emails (via SMTP or third-party service)

### 2. QR Code Generation API
- Endpoint to generate QR codes for events or related data.
- Configure QR code to comply with common QR Code v2 standards.

### 3. Reporting & Statistics API
- Generate reports by day or by specific content
- Design API for paginated reporting results
- Optionally export data (JSON, CSV…)

### 4. Write Unit Tests for API Authentication
- Use AI-generated test cases automatically.
- At least one Authentication API must be unit tested with coverage > 90%.
- Test scenarios:
  - Successful login
  - Failed login
  - Expired token
  - Invalid access rights