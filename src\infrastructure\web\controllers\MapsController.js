/**
 * Maps Controller
 * Handles HTTP requests for Google Maps Distance Matrix API
 */
class MapsController {
  constructor(googleMapsService) {
    this.googleMapsService = googleMapsService;
  }

  async getDistanceMatrix(req, res) {
    try {
      const { origins, destinations, options = {} } = req.body;

      // Validate required parameters
      if (!origins || !destinations) {
        return res.status(400).json({
          success: false,
          message: 'Origins and destinations are required'
        });
      }

      // Validate origins and destinations arrays
      const originsArray = Array.isArray(origins) ? origins : [origins];
      const destinationsArray = Array.isArray(destinations) ? destinations : [destinations];

      if (originsArray.length === 0 || destinationsArray.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Origins and destinations cannot be empty'
        });
      }

      // Validate array sizes (Google Maps API limits)
      if (originsArray.length > 25 || destinationsArray.length > 25) {
        return res.status(400).json({
          success: false,
          message: 'Maximum 25 origins and 25 destinations allowed'
        });
      }

      // Validate individual addresses
      for (const origin of originsArray) {
        await this.googleMapsService.validateAddress(origin);
      }

      for (const destination of destinationsArray) {
        await this.googleMapsService.validateAddress(destination);
      }

      // Validate options
      const validatedOptions = this.validateOptions(options);

      // Get distance matrix
      const result = await this.googleMapsService.getDistanceMatrix(
        originsArray,
        destinationsArray,
        validatedOptions
      );

      res.json({
        success: true,
        data: result,
        cached: result.cached || false
      });
    } catch (error) {
      const statusCode = error.message.includes('Invalid') ? 400 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  async getDistance(req, res) {
    try {
      const { origin, destination, options = {} } = req.query;

      if (!origin || !destination) {
        return res.status(400).json({
          success: false,
          message: 'Origin and destination are required'
        });
      }

      // Validate addresses
      await this.googleMapsService.validateAddress(origin);
      await this.googleMapsService.validateAddress(destination);

      // Validate options
      const validatedOptions = this.validateOptions(options);

      // Get distance matrix for single origin/destination
      const result = await this.googleMapsService.getDistanceMatrix(
        [origin],
        [destination],
        validatedOptions
      );

      // Extract single result
      const element = result.rows[0]?.elements[0];
      
      if (!element || element.status !== 'OK') {
        return res.status(400).json({
          success: false,
          message: 'Could not calculate distance between the specified locations'
        });
      }

      res.json({
        success: true,
        data: {
          origin: result.originAddresses[0],
          destination: result.destinationAddresses[0],
          distance: element.distance,
          duration: element.duration,
          durationInTraffic: element.durationInTraffic,
          fare: element.fare,
          status: element.status
        },
        cached: result.cached || false
      });
    } catch (error) {
      const statusCode = error.message.includes('Invalid') ? 400 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  async clearCache(req, res) {
    try {
      const { pattern } = req.query;
      
      // This would require implementing a cache clearing method in the cache service
      // For now, we'll return a success message
      res.json({
        success: true,
        message: 'Cache clearing is not implemented yet',
        pattern: pattern || 'google_maps:*'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getApiInfo(req, res) {
    try {
      res.json({
        success: true,
        data: {
          supportedModes: ['driving', 'walking', 'bicycling', 'transit'],
          supportedUnits: ['metric', 'imperial'],
          supportedLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'],
          supportedAvoidOptions: ['tolls', 'highways', 'ferries', 'indoor'],
          supportedTrafficModels: ['best_guess', 'pessimistic', 'optimistic'],
          supportedTransitModes: ['bus', 'subway', 'train', 'tram', 'rail'],
          supportedTransitRoutingPreferences: ['less_walking', 'fewer_transfers'],
          limits: {
            maxOrigins: 25,
            maxDestinations: 25,
            maxElementsPerRequest: 625,
            cacheExpiry: '1 hour'
          },
          examples: {
            singleDistance: '/api/maps/distance?origin=New York, NY&destination=Los Angeles, CA',
            distanceMatrix: '/api/maps/distance-matrix (POST with origins and destinations in body)',
            withOptions: '/api/maps/distance?origin=NYC&destination=LA&mode=driving&units=metric'
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  validateOptions(options) {
    const validatedOptions = {};

    // Validate mode
    if (options.mode) {
      const validModes = ['driving', 'walking', 'bicycling', 'transit'];
      if (!validModes.includes(options.mode)) {
        throw new Error(`Invalid mode. Supported modes: ${validModes.join(', ')}`);
      }
      validatedOptions.mode = options.mode;
    }

    // Validate units
    if (options.units) {
      const validUnits = ['metric', 'imperial'];
      if (!validUnits.includes(options.units)) {
        throw new Error(`Invalid units. Supported units: ${validUnits.join(', ')}`);
      }
      validatedOptions.units = options.units;
    }

    // Validate language
    if (options.language) {
      const validLanguages = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'];
      if (!validLanguages.includes(options.language)) {
        throw new Error(`Invalid language. Supported languages: ${validLanguages.join(', ')}`);
      }
      validatedOptions.language = options.language;
    }

    // Validate avoid
    if (options.avoid) {
      const validAvoidOptions = ['tolls', 'highways', 'ferries', 'indoor'];
      const avoidArray = Array.isArray(options.avoid) ? options.avoid : [options.avoid];
      
      for (const avoid of avoidArray) {
        if (!validAvoidOptions.includes(avoid)) {
          throw new Error(`Invalid avoid option. Supported options: ${validAvoidOptions.join(', ')}`);
        }
      }
      validatedOptions.avoid = avoidArray.join('|');
    }

    // Validate traffic model
    if (options.trafficModel) {
      const validTrafficModels = ['best_guess', 'pessimistic', 'optimistic'];
      if (!validTrafficModels.includes(options.trafficModel)) {
        throw new Error(`Invalid traffic model. Supported models: ${validTrafficModels.join(', ')}`);
      }
      validatedOptions.trafficModel = options.trafficModel;
    }

    // Validate transit mode
    if (options.transitMode) {
      const validTransitModes = ['bus', 'subway', 'train', 'tram', 'rail'];
      const transitModeArray = Array.isArray(options.transitMode) ? options.transitMode : [options.transitMode];
      
      for (const mode of transitModeArray) {
        if (!validTransitModes.includes(mode)) {
          throw new Error(`Invalid transit mode. Supported modes: ${validTransitModes.join(', ')}`);
        }
      }
      validatedOptions.transitMode = transitModeArray.join('|');
    }

    // Validate transit routing preference
    if (options.transitRoutingPreference) {
      const validPreferences = ['less_walking', 'fewer_transfers'];
      if (!validPreferences.includes(options.transitRoutingPreference)) {
        throw new Error(`Invalid transit routing preference. Supported preferences: ${validPreferences.join(', ')}`);
      }
      validatedOptions.transitRoutingPreference = options.transitRoutingPreference;
    }

    // Validate departure time (should be a future timestamp)
    if (options.departureTime) {
      const departureTime = parseInt(options.departureTime);
      if (isNaN(departureTime) || departureTime <= Math.floor(Date.now() / 1000)) {
        throw new Error('Departure time must be a future timestamp');
      }
      validatedOptions.departureTime = departureTime;
    }

    // Validate arrival time (should be a future timestamp)
    if (options.arrivalTime) {
      const arrivalTime = parseInt(options.arrivalTime);
      if (isNaN(arrivalTime) || arrivalTime <= Math.floor(Date.now() / 1000)) {
        throw new Error('Arrival time must be a future timestamp');
      }
      validatedOptions.arrivalTime = arrivalTime;
    }

    return validatedOptions;
  }
}

module.exports = MapsController;
