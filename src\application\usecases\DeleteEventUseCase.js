/**
 * Delete Event Use Case
 * Application layer use case for deleting events
 */
class DeleteEventUseCase {
  constructor(eventRepository, userRepository, emailService, cacheService) {
    this.eventRepository = eventRepository;
    this.userRepository = userRepository;
    this.emailService = emailService;
    this.cacheService = cacheService;
  }

  async execute(eventId, userId) {
    // Get existing event
    const existingEvent = await this.eventRepository.findById(eventId);
    if (!existingEvent) {
      throw new Error('Event not found');
    }

    // Check if user has permission to delete this event
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Only admin or the event organizer can delete the event
    if (user.role !== 'admin' && existingEvent.organizerId !== userId) {
      throw new Error('You do not have permission to delete this event');
    }

    // Check if event can be deleted (business rules)
    await this.validateDeletion(existingEvent);

    // Notify attendees before deletion
    if (existingEvent.attendees.length > 0) {
      await this.notifyAttendeesOfCancellation(existingEvent);
    }

    // Delete the event
    await this.eventRepository.delete(eventId);

    // Clear related cache entries
    await this.clearEventCache(eventId);

    return {
      id: eventId,
      message: 'Event deleted successfully'
    };
  }

  async validateDeletion(event) {
    // Check if event has already started
    const now = new Date();
    if (event.startDateTime <= now) {
      throw new Error('Cannot delete an event that has already started');
    }

    // Check if event is too close to start time (e.g., less than 24 hours)
    const hoursUntilStart = (event.startDateTime - now) / (1000 * 60 * 60);
    if (hoursUntilStart < 24 && event.attendees.length > 0) {
      throw new Error('Cannot delete an event with attendees less than 24 hours before start time');
    }

    // Additional business rules can be added here
    // For example: check if event has paid attendees, check cancellation policy, etc.
  }

  async notifyAttendeesOfCancellation(event) {
    if (!this.emailService || event.attendees.length === 0) {
      return;
    }

    try {
      for (const attendee of event.attendees) {
        await this.emailService.sendEventCancellationNotification(
          attendee.email,
          event
        );
      }
    } catch (error) {
      // Log error but don't fail the deletion operation
      console.error('Failed to send cancellation notifications:', error);
    }
  }

  async clearEventCache(eventId) {
    if (!this.cacheService) {
      return;
    }

    try {
      // Clear specific event cache
      await this.cacheService.del(`event:${eventId}`);
      
      // Clear events list cache (since the list has changed)
      const cacheKeys = await this.cacheService.keys('events:*');
      if (cacheKeys.length > 0) {
        await this.cacheService.del(...cacheKeys);
      }
    } catch (error) {
      // Log error but don't fail the deletion operation
      console.error('Failed to clear event cache:', error);
    }
  }

  async softDelete(eventId, userId) {
    // Alternative method for soft deletion (marking as deleted instead of removing)
    const existingEvent = await this.eventRepository.findById(eventId);
    if (!existingEvent) {
      throw new Error('Event not found');
    }

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (user.role !== 'admin' && existingEvent.organizerId !== userId) {
      throw new Error('You do not have permission to delete this event');
    }

    // Update event status to cancelled instead of deleting
    const updatedEvent = await this.eventRepository.update(eventId, {
      status: 'cancelled',
      deletedAt: new Date(),
      deletedBy: userId
    });

    // Notify attendees of cancellation
    if (existingEvent.attendees.length > 0) {
      await this.notifyAttendeesOfCancellation(existingEvent);
    }

    // Clear cache
    await this.clearEventCache(eventId);

    return updatedEvent;
  }
}

module.exports = DeleteEventUseCase;
