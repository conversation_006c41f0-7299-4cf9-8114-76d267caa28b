module.exports = {
  // Test environment
  testEnvironment: 'node',

  // Test file patterns
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js',
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],

  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js',
    '!src/**/*.spec.js',
    '!src/app.js',
    '!src/config/**'
  ],

  // Coverage thresholds (>90% as required)
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    },
    // Authentication APIs must have >90% coverage
    './src/application/usecases/AuthenticateUserUseCase.js': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    './src/application/usecases/RegisterUserUseCase.js': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    './src/infrastructure/web/controllers/AuthController.js': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    './src/infrastructure/web/middleware/authMiddleware.js': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    }
  },

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

  // Module paths
  moduleDirectories: ['node_modules', 'src'],

  // Clear mocks between tests
  clearMocks: true,

  // Verbose output
  verbose: true,

  // Test timeout
  testTimeout: 10000
};
