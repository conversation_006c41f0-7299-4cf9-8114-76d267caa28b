/**
 * Event Controller
 * Handles HTTP requests for event operations
 */
class EventController {
  constructor(createEventUseCase, getEventsUseCase, updateEventUseCase, deleteEventUseCase, registerAttendeeUseCase, unregisterAttendeeUseCase) {
    this.createEventUseCase = createEventUseCase;
    this.getEventsUseCase = getEventsUseCase;
    this.updateEventUseCase = updateEventUseCase;
    this.deleteEventUseCase = deleteEventUseCase;
    this.registerAttendeeUseCase = registerAttendeeUseCase;
    this.unregisterAttendeeUseCase = unregisterAttendeeUseCase;
  }

  async createEvent(req, res) {
    try {
      const eventData = req.body;
      const userId = req.user.userId;

      const event = await this.createEventUseCase.execute(eventData, userId);

      res.status(201).json({
        success: true,
        data: event,
        message: 'Event created successfully'
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error.message
      });
    }
  }

  async getEvents(req, res) {
    try {
      const filters = {
        status: req.query.status,
        organizerId: req.query.organizerId,
        startDate: req.query.startDate,
        endDate: req.query.endDate
      };

      const pagination = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10
      };

      const result = await this.getEventsUseCase.execute(filters, pagination);

      res.json({
        success: true,
        data: result.events,
        pagination: result.pagination
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getEventById(req, res) {
    try {
      const eventId = req.params.id;
      const event = await this.getEventsUseCase.executeById(eventId);

      res.json({
        success: true,
        data: event
      });
    } catch (error) {
      const statusCode = error.message === 'Event not found' ? 404 : 500;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  async updateEvent(req, res) {
    try {
      const eventId = req.params.id;
      const eventData = req.body;
      const userId = req.user.userId;

      if (!this.updateEventUseCase) {
        return res.status(501).json({
          success: false,
          message: 'Update event functionality is not implemented yet'
        });
      }

      const event = await this.updateEventUseCase.execute(eventId, eventData, userId);

      res.json({
        success: true,
        data: event,
        message: 'Event updated successfully'
      });
    } catch (error) {
      const statusCode = error.message === 'Event not found' ? 404 :
                        error.message.includes('permission') ? 403 : 400;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  async deleteEvent(req, res) {
    try {
      const eventId = req.params.id;
      const userId = req.user.userId;

      if (!this.deleteEventUseCase) {
        return res.status(501).json({
          success: false,
          message: 'Delete event functionality is not implemented yet'
        });
      }

      const result = await this.deleteEventUseCase.execute(eventId, userId);

      res.json({
        success: true,
        data: result,
        message: 'Event deleted successfully'
      });
    } catch (error) {
      const statusCode = error.message === 'Event not found' ? 404 :
                        error.message.includes('permission') ? 403 : 400;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  async searchEvents(req, res) {
    try {
      const query = req.query.q;
      if (!query) {
        return res.status(400).json({
          success: false,
          message: 'Search query is required'
        });
      }

      const pagination = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10
      };

      const result = await this.getEventsUseCase.searchEvents(query, pagination);

      res.json({
        success: true,
        data: result.events,
        pagination: result.pagination
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async registerForEvent(req, res) {
    try {
      const eventId = req.params.id;
      const attendeeEmail = req.body.email || req.user.email;

      if (!this.registerAttendeeUseCase) {
        return res.status(501).json({
          success: false,
          message: 'Event registration functionality is not implemented yet'
        });
      }

      const result = await this.registerAttendeeUseCase.execute(eventId, attendeeEmail);

      res.json({
        success: true,
        data: result,
        message: 'Successfully registered for event'
      });
    } catch (error) {
      const statusCode = error.message === 'Event not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }

  async unregisterFromEvent(req, res) {
    try {
      const eventId = req.params.id;
      const attendeeEmail = req.body.email || req.user.email;

      if (!this.unregisterAttendeeUseCase) {
        return res.status(501).json({
          success: false,
          message: 'Event unregistration functionality is not implemented yet'
        });
      }

      const result = await this.unregisterAttendeeUseCase.execute(eventId, attendeeEmail);

      res.json({
        success: true,
        data: result,
        message: 'Successfully unregistered from event'
      });
    } catch (error) {
      const statusCode = error.message === 'Event not found' ? 404 : 400;
      res.status(statusCode).json({
        success: false,
        message: error.message
      });
    }
  }
}

module.exports = EventController;
