/**
 * Unregister Attendee Use Case
 * Application layer use case for unregistering attendees from events
 */
class UnregisterAttendeeUseCase {
  constructor(eventRepository, userRepository, emailService) {
    this.eventRepository = eventRepository;
    this.userRepository = userRepository;
    this.emailService = emailService;
  }

  async execute(eventId, attendeeEmail) {
    // Get the event
    const event = await this.eventRepository.findById(eventId);
    if (!event) {
      throw new Error('Event not found');
    }

    // Validate email format
    if (!this.isValidEmail(attendeeEmail)) {
      throw new Error('Invalid email address');
    }

    // Find the attendee
    const attendeeIndex = event.attendees.findIndex(
      attendee => attendee.email.toLowerCase() === attendeeEmail.toLowerCase()
    );

    if (attendeeIndex === -1) {
      throw new Error('You are not registered for this event');
    }

    // Check if unregistration is allowed
    await this.validateUnregistration(event);

    // Remove the attendee
    const removedAttendee = event.attendees[attendeeIndex];
    event.attendees.splice(attendeeIndex, 1);

    // Update the event
    const updatedEvent = await this.eventRepository.update(eventId, {
      attendees: event.attendees
    });

    // Process waitlist if there's space available
    const waitlistResult = await this.processWaitlist(eventId, updatedEvent);

    // Send unregistration confirmation
    await this.sendUnregistrationConfirmation(attendeeEmail, updatedEvent, removedAttendee);

    return {
      event: updatedEvent,
      removedAttendee: removedAttendee,
      waitlistProcessed: waitlistResult,
      message: 'Successfully unregistered from the event'
    };
  }

  async validateUnregistration(event) {
    // Check if event has already started
    if (event.startDateTime <= new Date()) {
      throw new Error('Cannot unregister from an event that has already started');
    }

    // Check if unregistration is allowed within a certain timeframe
    const hoursUntilStart = (event.startDateTime - new Date()) / (1000 * 60 * 60);
    if (hoursUntilStart < 24) {
      throw new Error('Cannot unregister less than 24 hours before the event starts');
    }

    // Additional business rules can be added here
    // For example: check cancellation policy, refund rules, etc.
  }

  async processWaitlist(eventId, event) {
    // If there's no waitlist or event is at capacity, return
    if (!event.waitlist || event.waitlist.length === 0) {
      return null;
    }

    if (event.maxAttendees && event.attendees.length >= event.maxAttendees) {
      return null;
    }

    // Get the first person from waitlist
    const nextInLine = event.waitlist[0];
    
    // Remove from waitlist
    event.waitlist.splice(0, 1);

    // Update positions for remaining waitlist entries
    event.waitlist.forEach((entry, index) => {
      entry.position = index + 1;
    });

    // Add to attendees
    const newAttendee = {
      email: nextInLine.email,
      registeredAt: new Date(),
      status: 'registered',
      promotedFromWaitlist: true,
      originalWaitlistPosition: nextInLine.position
    };

    // Get user information if available
    const user = await this.userRepository.findByEmail(nextInLine.email);
    if (user) {
      newAttendee.userId = user.id;
      newAttendee.firstName = user.firstName;
      newAttendee.lastName = user.lastName;
    }

    event.attendees.push(newAttendee);

    // Update the event
    const updatedEvent = await this.eventRepository.update(eventId, {
      attendees: event.attendees,
      waitlist: event.waitlist
    });

    // Send promotion notification
    await this.sendWaitlistPromotionNotification(nextInLine.email, updatedEvent, newAttendee);

    return {
      promotedAttendee: newAttendee,
      remainingWaitlist: event.waitlist.length
    };
  }

  async sendUnregistrationConfirmation(email, event, removedAttendee) {
    if (!this.emailService) {
      return;
    }

    try {
      await this.emailService.sendEventUnregistrationConfirmation(
        email,
        event,
        removedAttendee
      );
    } catch (error) {
      // Log error but don't fail the unregistration
      console.error('Failed to send unregistration confirmation:', error);
    }
  }

  async sendWaitlistPromotionNotification(email, event, attendee) {
    if (!this.emailService) {
      return;
    }

    try {
      await this.emailService.sendWaitlistPromotionNotification(
        email,
        event,
        attendee
      );
    } catch (error) {
      // Log error but don't fail the operation
      console.error('Failed to send waitlist promotion notification:', error);
    }
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  async removeFromWaitlist(eventId, attendeeEmail) {
    const event = await this.eventRepository.findById(eventId);
    if (!event) {
      throw new Error('Event not found');
    }

    if (!event.waitlist) {
      throw new Error('You are not on the waitlist for this event');
    }

    // Find the waitlist entry
    const waitlistIndex = event.waitlist.findIndex(
      entry => entry.email.toLowerCase() === attendeeEmail.toLowerCase()
    );

    if (waitlistIndex === -1) {
      throw new Error('You are not on the waitlist for this event');
    }

    // Remove from waitlist
    const removedEntry = event.waitlist[waitlistIndex];
    event.waitlist.splice(waitlistIndex, 1);

    // Update positions for remaining entries
    event.waitlist.forEach((entry, index) => {
      entry.position = index + 1;
    });

    // Update the event
    const updatedEvent = await this.eventRepository.update(eventId, {
      waitlist: event.waitlist
    });

    return {
      event: updatedEvent,
      removedEntry: removedEntry,
      message: 'Successfully removed from waitlist'
    };
  }

  async bulkUnregister(eventId, attendeeEmails) {
    const results = {
      successful: [],
      failed: []
    };

    for (const email of attendeeEmails) {
      try {
        const result = await this.execute(eventId, email);
        results.successful.push({
          email: email,
          attendee: result.removedAttendee
        });
      } catch (error) {
        results.failed.push({
          email: email,
          error: error.message
        });
      }
    }

    return results;
  }
}

module.exports = UnregisterAttendeeUseCase;
