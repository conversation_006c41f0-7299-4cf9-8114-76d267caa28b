{"name": "ai-skill", "version": "1.0.0", "description": "A comprehensive event management API with authentication, rate limiting, and external integrations", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "format:check": "prettier --check src/"}, "keywords": ["event-management", "api", "nodejs", "express", "mongodb", "redis", "jwt", "clean-architecture"], "author": "AI Skill Team", "engines": {"node": ">=14.0.0"}, "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "nodemailer": "^7.0.4", "qrcode": "^1.5.4", "rate-limit-redis": "^4.2.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"eslint": "^9.30.1", "jest": "^30.0.4", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.1.1"}}